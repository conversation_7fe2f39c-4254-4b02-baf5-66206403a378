<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.9.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.2.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj" />
  </ItemGroup>

</Project>
