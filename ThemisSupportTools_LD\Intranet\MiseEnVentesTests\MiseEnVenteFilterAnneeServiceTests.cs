﻿using MiseEnVentes.Models;
using MiseEnVentes.ServiceUnitaire;
using Xunit;


//Test pour le filtre du tableaux par année
public class MiseEnVenteFilterAnneeServiceTests
{
    [Fact]
    public async Task FilterByYearAsync_Should_Filter_By_CurrentDate_When_YearIsZero()
    {
        // Arrange
        var service = new MiseEnVenteFilterServiceAnnee();
        var today = DateOnly.FromDateTime(DateTime.Today);

        var list = new List<MiseEnVenteNewForTest>
        {
            new MiseEnVenteNewForTest { Date = today.AddDays(-1) },
            new MiseEnVenteNewForTest { Date = today },
            new MiseEnVenteNewForTest { Date = today.AddDays(1) }
        };

        // Act
        var result = await service.FilterByYearAsync(list, 0);

        // Assert
        Assert.DoesNotContain(result, m => m.Date < today);
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task FilterByYearAsync_Should_Filter_By_SelectedYear()
    {
        // Arrange
        var service = new MiseEnVenteFilterServiceAnnee();

        var list = new List<MiseEnVenteNewForTest>
        {
            new MiseEnVenteNewForTest { Date = new DateOnly(2023, 5, 10) },
            new MiseEnVenteNewForTest { Date = new DateOnly(2024, 3, 15) },
            new MiseEnVenteNewForTest { Date = new DateOnly(2024, 12, 1) }
        };

        // Act
        var result = await service.FilterByYearAsync(list, 2024);

        // Assert
        Assert.All(result, m => Assert.Equal(2024, m.Date?.Year));
        Assert.Equal(2, result.Count);
    }
}
