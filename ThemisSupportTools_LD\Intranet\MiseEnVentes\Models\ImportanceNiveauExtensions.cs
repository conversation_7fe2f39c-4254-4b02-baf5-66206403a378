﻿using MiseEnVentes.Models;

public static class ImportanceNiveauExtensions
{
    public static string GetLabel(this ImportanceNiveau niveau) => niveau switch
    {
        ImportanceNiveau.Normal => "Normal",
        ImportanceNiveau.Moyen => "Moyen",
        ImportanceNiveau.Eleve => "Élevé",
        _ => "Inconnu"
    };

    public static string GetBadgeClass(this ImportanceNiveau niveau) => niveau switch
    {
        ImportanceNiveau.Normal => "badge bg-success",
        ImportanceNiveau.Moyen => "badge bg-warning text-dark",
        ImportanceNiveau.Eleve => "badge bg-danger",
        _ => "badge bg-secondary"
    };
}
