﻿h3 {
    text-align: left;
    margin-top: 100px;
    margin-left: 20px;
    color: #212529;
    font-family : <PERSON><PERSON>s, sans-serif;
}

h3.Titre-intervenant {
    text-align: left;
    margin-top: 100px;
    margin-left: 20px;
    color: #212529;
    font-family: Pop<PERSON>s, sans-serif;
}


.header-actions-intervenant h3 {
    margin: 0;
    color: #2c3e50;
}

.header-actions-intervenant button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 10rem;
    background-color: #f4b400;
}

.header-actions-intervenant {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px;
    padding-top: 60px;
}




.table {
    width: 90%;
    margin: auto;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.table th {
    background-color: #3498db;
    color: white;
    text-align: center;
}

.table td {
    text-align: center;
}

p {
    text-align: center;
    font-style: italic;
    color: #888;
}



.header-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px; 
    margin: 20px;
    padding-top:60px;
}

.header-actions h3 {
    margin: 0;
    color: #2c3e50;
}

.header-actions button {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 10rem;
    background-color: #f4b400;
}



.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
}

.popup-content {
    background-color: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    width: 400px;
    z-index: 1051;
}



.text-success {
    color: green;
}

.text-secondary {
    color: gray;
}

.text-lightgray {
    color: lightgray;
}


.icon-button {
    background-color: lightgray;
    border-radius: 8px;
    padding: 6px 10px;
    display: inline-block;
    transition: background-color 0.2s ease;
}

.icon-button.mail-true {
    background-color: #d4edda; 
    color: #155724;
}

.icon-button.ticket-true {
    background-color: #d1ecf1; 
    color: #0c5460;
}

.icon-button:hover {
    cursor: pointer;
    filter: brightness(1.1);
}

.table-bordered td {
    border: 1px solid black;
}


.toast-container {
    z-index: 1055;
}

.toast-custom {
    background-color: #28a745;
    color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: none;
    font-size: 0.95rem;
    animation: fadeInSlide 0.5s ease-out;
}

.toast-custom .toast-header {
    background-color: transparent;
    border-bottom: none;
    color: #fff;
    font-weight: 600;
}

.toast-custom .btn-close {
    filter: invert(1);
}


/* Toast suppression - rouge */
.toast-custom-delete {
    background-color: #dc3545; 
    color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: none;
    font-size: 0.95rem;
    animation: fadeInSlide 0.5s ease-out;
}

.toast-custom-delete .toast-header {
    background-color: transparent;
    border-bottom: none;
    color: #fff;
    font-weight: 600;
}

.toast-custom-delete .btn-close {
    filter: invert(1); 
}

.highlight-row {
    animation: flashHighlight 2.5s ease-in-out;
    background-color: #d4edda; /* Vert pâle */
}

@keyframes flashHighlight {
    0% {
        background-color: #d4edda;
    }

    100% {
        background-color: transparent;
    }
}





/* Animation d’apparition */
@keyframes fadeInSlide {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

