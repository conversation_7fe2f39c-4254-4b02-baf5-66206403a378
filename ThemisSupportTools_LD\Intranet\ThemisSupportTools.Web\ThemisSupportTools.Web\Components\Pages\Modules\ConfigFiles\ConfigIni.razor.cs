﻿using Core.Themis.Libraries.BLL.Services;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Structure;
using Core.Themis.Libraries.Razor.Common.ViewModels;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.JSInterop;


namespace ThemisSupportTools.Web.Components.Pages.Modules.ConfigFiles
{
    public partial class ConfigIni : IDisposable
    {
        const string MODULE_NAME = "config-ini";
        private TstRoleModel TstRole = new();
        private TstConfigIniViewModel TstConfigIniViewModel = new();
        private Modal modal = default!;
        private bool showConfigIniKeys = false;
        private string _currentUri;
        private bool _hasChanges = false;

        private List<ConfigIniSectionDTO> NonMandatorySections = new();

        List<ToastMessage> toastMessages = new List<ToastMessage>();
        ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;
        [Inject] private CustomCircuitService CustomCircuitService { get; set; } = default!;

        TimeSpan TimeLeft;
        private ConfirmDialog dialog = new();
        public string FormClass { get; set; } = "needs-validation";
        private CancellationTokenSource _timerCts = new();
        private TimeSpan _initialTime;
        private bool _confirmDialogActive = false;

        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private IThemisSupportToolsManager ThemisSupportToolsManager { get; set; } = default!;
        [Inject] private IJSRuntime JsRuntime { get; set; } = default!;
        [Inject] private ITstAccessService TstAccessService { get; set; } = default!;
        [Inject] private IJSRuntime _js { get; set; } = default!;
        [Inject] private IConfiguration Configuration { get; set; } = default!;
        [Inject] private NavigationManager Navigation { get; set; } = default!;
        [Inject] public required IStringLocalizer<Resource> Localizer { get; set; }
        [Inject] private IWsAdminStructuresManager WsAdminManager { get; set; } = default!;
        private List<ConfigIniSectionDTO> mandatorySections = new List<ConfigIniSectionDTO>();

        [Parameter]
        public int? StructureId { get; set; }

        private string structureName = string.Empty;

        public List<WsAdminStructureDTO> Structures { get; set; } = new();

        protected override void OnInitialized()
        {
            _currentUri = Navigation.Uri;
            Navigation.LocationChanged += OnLocationChanged;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                if (StructureId is null)
                {
                    NavigationManager.NavigateTo(Navigation.BaseUri + "choose-structure/config-ini");
                }
                else
                {
                    structureName = TstAccessService.StructureName;

                    if (TstAccessService.IsGranted(MODULE_NAME))
                    {
                        TstRole = TstAccessService.GetRoleByModuleName(MODULE_NAME);

                        if (!TstRole.CanRead)
                        {
                            NavigationManager.NavigateTo("");
                        }

                        string currentUser = TstAccessService.GetUserName();
                        CustomCircuitService.CurrentUser = currentUser;
                        CustomCircuitService.StructureId = StructureId.Value;

                        TstConfigIniViewModel = await ThemisSupportToolsManager.GetConfigIniAsync(StructureId.Value, currentUser);

                        if (TstConfigIniViewModel.ConfigIniAllSections != null)
                        {
                            mandatorySections = TstConfigIniViewModel.ConfigIniAllSections
                                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                                .Select(s => new ConfigIniSectionDTO
                                {
                                    SectionName = s.SectionName,
                                    SectionFields = s.SectionFields
                                }).ToList();
                        }


                        if (!string.IsNullOrEmpty(TstConfigIniViewModel.Error))
                        {
                            var options = new ConfirmDialogOptions
                            {
                                YesButtonText = Localizer["change_structure_button_btnDanger"],
                                YesButtonColor = ButtonColor.Success,
                                NoButtonText = string.Empty,
                            };
                            await ShowConfirmationAsync(options, "Themis Support Tools", Localizer["file_in_use_message", TstConfigIniViewModel.UserNameWithTempFile.ToUpper(), StructureId.Value]);

                            NavigationManager.NavigateTo(Navigation.BaseUri + "choose-structure/config-ini");
                        }

                        if (TstConfigIniViewModel.ConfigIniSectionsOfUser != null)
                        {
                            TstConfigIniViewModel.ConfigIniSectionsOfUser.ForEach(x =>
                            {
                                x.SectionFields.ForEach(k =>
                                {
                                    if (k.IsMandatory)
                                        k.MandatoryMessage = Localizer[k.MandatoryMessage];
                                });
                            });
                        }
                    }
                    else
                    {
                        NavigationManager.NavigateTo("");
                    }
                }

                InitializeTimer();
                await StartTimer();
            }
        }


        private void InitializeTimer()
        {
            if (int.TryParse(Configuration["timerInMinute"], out int timerInMinutes))
            {
                _initialTime = new TimeSpan(0, timerInMinutes, 0);
            }
            else
            {
                _initialTime = new TimeSpan(0, 5, 0);
            }
            TimeLeft = _initialTime;
        }

        private async Task<bool> ShowConfirmationAsync(ConfirmDialogOptions dialogOptions, string title, string message)
        {
            var confirmation = await dialog.ShowAsync(
                title: title,
                message1: message,
                confirmDialogOptions: dialogOptions
            );

            return confirmation;
        }

        private async Task<bool> ShowConfirmationWithTimerAsync(string title, ConfirmDialogOptions options)
        {
            _confirmDialogActive = true;
            try
            {
                while (TimeLeft > TimeSpan.Zero)
                {
                    var message = string.Format(Localizer["time_warning_message"], TimeLeft.Minutes, TimeLeft.Seconds);
                    var confirmationTask = dialog.ShowAsync(
                        title: title,
                        message1: message,
                        confirmDialogOptions: options
                    );

                    await Task.Delay(millisecondsDelay: 1000);
                    TimeLeft = TimeLeft.Subtract(new TimeSpan(0, 0, 1));
                    StateHasChanged();

                    if (confirmationTask.IsCompleted)
                    {
                        bool result = await confirmationTask;
                        if (result)
                        {
                            // L'utilisateur a cliqué sur Oui
                            ResetTimer();
                        }
                        else
                        {
                            DeleteTmpFile();
                            NavigationManager.NavigateTo("choose-structure/config-ini");
                        }
                        return result;
                    }
                }

                // Si le temps est écoulé pendant l'affichage de la boîte de dialogue
                DeleteTmpFile();
                NavigationManager.NavigateTo("choose-structure/config-ini");
                return false;
            }
            finally
            {
                _confirmDialogActive = false;
            }
        }

        private void ScrollToTop()
        {
            _js.InvokeVoidAsync("scrollToTop");
        }

        private void OnLocationChanged(object sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
        {
            if (_currentUri != e.Location)
            {
                DeleteTmpFile();
                _currentUri = e.Location;
            }
        }

        // Méthode pour supprimer le fichier temporaire
        private void DeleteTmpFile()
        {
            if (StructureId.HasValue)
            {
                ThemisSupportToolsManager.DeleteConfigIniTempFile(StructureId.Value, TstAccessService.GetUserName());
                _hasChanges = false;
            }

            StopTimer();
        }

        public void Dispose()
        {
            Navigation.LocationChanged -= OnLocationChanged;
            StopTimer();

            CustomCircuitService.CurrentUser = null;
            CustomCircuitService.StructureId = 0;
        }

        /// <summary>
        /// Change la valeur d'un champ de section de configuration INI.
        /// Si le champ appartient à un groupe, les données de sélection sont mises à jour.
        /// Si le champ est connecté à un groupe, une fenêtre modale est affichée.
        /// </summary>
        /// <param name="field"></param>
        async Task ChangeValue(ConfigIniSectionFieldDTO field)
        {
            if (field is not null)
            {

                if (!string.IsNullOrWhiteSpace(field.Groupe))
                {
                    ThemisSupportToolsManager.SetSelectDatas(TstConfigIniViewModel.ConfigIniSectionsOfUser);
                }

                if (!string.IsNullOrWhiteSpace(field.ConnectedToGroup))
                {
                    showConfigIniKeys = true;
                    await ShowModal();
                }

                _hasChanges = true;
                ResetTimer();
            }
        }

        private async Task TimerLoopAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && TimeLeft > TimeSpan.Zero)
                {
                    // Vérifier si une boîte de dialogue est déjà active
                    if (_confirmDialogActive)
                    {
                        await Task.Delay(500, cancellationToken);
                        continue;
                    }

                    if (TimeLeft.TotalMinutes is > 1 and <= 2)
                    {
                        var options = new ConfirmDialogOptions
                        {
                            YesButtonText = Localizer["confirm_dialog_yes_button"],
                            YesButtonColor = ButtonColor.Success,
                            NoButtonText = Localizer["confirm_dialog_no_button"],
                            NoButtonColor = ButtonColor.Danger
                        };

                        await ShowConfirmationWithTimerAsync(
                            title: "Themis Support Tools",
                            options: options
                        );

                        return;
                    }

                    await Task.Delay(1000, cancellationToken);
                    TimeLeft = TimeLeft.Subtract(TimeSpan.FromSeconds(1));
                    StateHasChanged();
                }

                // Si le temps est écoulé, rediriger l'utilisateur
                if (TimeLeft <= TimeSpan.Zero && !cancellationToken.IsCancellationRequested)
                {
                    DeleteTmpFile();
                    NavigationManager.NavigateTo("choose-structure/config-ini");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur dans le timer : {ex.Message}");
            }
        }

        private void StopTimer()
        {
            try
            {
                _timerCts.Cancel();
                _timerCts.Dispose();
                _timerCts = new CancellationTokenSource();
            }
            catch
            {

            }
        }

        private void ResetTimer()
        {
            StopTimer();
            InitializeTimer();
            _ = StartTimer();
        }

        private async Task StartTimer()
        {
            StopTimer();
            _timerCts = new CancellationTokenSource();
            await TimerLoopAsync(_timerCts.Token);
        }

        /// <summary>
        /// Ajout et la suppression d'un prestateur de paiement
        /// </summary>
        /// <param name="section"></param>
        /// <returns></returns>
        async Task ChangeValueForSectionGroup(ConfigIniSectionDTO section)
        {
            if (section is not null)
            {
                if (section.EventToClick.Equals("Add", StringComparison.OrdinalIgnoreCase))
                {
                    section.IsChecked = true;

                    NonMandatorySections = GetNonMandatorySections(section.SectionName);

                    showConfigIniKeys = true;
                    await ShowModal();
                }
                else
                {
                    section.IsChecked = false;
                }

                SwitchChanged(section);
                ResetTimer();
            }
        }

        /// <summary>
        /// Suppression de la section dans la liste des parametres avancées
        /// </summary>
        /// <param name="section"></param>
        private void RemoveSection(ConfigIniSectionDTO section)
        {
            Console.WriteLine($"[DEBUG] RemoveSection appelée pour: {section.SectionName}");
            var sectionToRemove = TstConfigIniViewModel.ConfigIniSectionsOfUser!.FirstOrDefault(s => s.SectionName == section.SectionName);

            if (sectionToRemove is not null && !string.IsNullOrEmpty(sectionToRemove.SectionGroupe))
            {
                var fieldsConnectedToGroup = TstConfigIniViewModel.ConfigIniSectionsOfUser!.SelectMany(s => s.SectionFields.Where(f => f.ConnectedToGroup == sectionToRemove.SectionGroupe));

                foreach (var item in fieldsConnectedToGroup)
                {
                    item.FieldMultipleValue = item.FieldMultipleValue.Where(o => o != sectionToRemove.SectionName).ToArray();
                    item.FieldValue = string.Join(",", item.FieldMultipleValue);
                }
            }

            if (sectionToRemove != null)
            {
                TstConfigIniViewModel.ConfigIniSectionsOfUser!.Remove(sectionToRemove);
                sectionToRemove.IsChecked = false;

                // Synchronisation bidirectionnelle : mettre à jour la section correspondante dans la popup des sections non obligatoires
                var sectionInPopup = GetNonMandatorySections().FirstOrDefault(s => s.SectionName == section.SectionName);
                if (sectionInPopup is not null)
                {
                    sectionInPopup.IsChecked = false;
                }

                // Synchronisation bidirectionnelle : mettre à jour les champs PRESTATAIREPAIEMENT qui référencent cette section
                // Chercher dans toutes les sections (pas seulement les obligatoires) les champs qui ont cette section dans leurs valeurs multiples
                var allFieldsWithThisSection = TstConfigIniViewModel.ConfigIniSectionsOfUser!
                    .SelectMany(s => s.SectionFields)
                    .Where(f => f.FieldMultipleValue != null && f.FieldMultipleValue.Contains(section.SectionName))
                    .ToList();

                foreach (var field in allFieldsWithThisSection)
                {
                    // Retirer la section de la liste des valeurs multiples
                    // Créer un nouveau tableau pour forcer la mise à jour du composant Select2
                    var newValues = field.FieldMultipleValue
                        .Where(v => v != section.SectionName).ToArray();
                    field.FieldMultipleValue = newValues;
                    field.FieldValue = string.Join(",", newValues);

                    // Marquer le champ comme modifié pour forcer la mise à jour de l'interface
                    field.IsModified = true;

                    // Ajouter un timestamp pour forcer la mise à jour du composant Select2
                    if (field.HtmlAttributes == null)
                        field.HtmlAttributes = new Dictionary<string, object>();
                    field.HtmlAttributes["data-update-timestamp"] = DateTime.Now.Ticks.ToString();
                }

                StateHasChanged();
            }
        }

        /// <summary>
        /// Met à jour l'état de la section spécifiée en fonction de son état actuel.
        /// Si la section est cochée, elle est ajoutée à la liste des sections de l'utilisateur.
        /// Si la section est décochée, elle est supprimée de cette liste.
        /// </summary>
        /// <param name="section"> </param>
        private void SwitchChanged(ConfigIniSectionDTO section)
        {
            var sectionNonMandatoryForPopup = GetNonMandatorySections().FirstOrDefault(s => s.SectionName == section.SectionName);
            if (sectionNonMandatoryForPopup is not null)
            {
                sectionNonMandatoryForPopup.IsChecked = section.IsChecked;
            }

            if (section.IsChecked)
            {
                if (!TstConfigIniViewModel.ConfigIniSectionsOfUser!.Any(s => s.SectionName == section.SectionName && s.ChildrenHasValue))
                {
                    TstConfigIniViewModel.ConfigIniSectionsOfUser!.Add(sectionNonMandatoryForPopup);
                    sectionNonMandatoryForPopup.SectionFields.ForEach(f => f.IsInCustmerXml = true);

                }
            }
            else
            {
                RemoveSection(section);
            }
            StateHasChanged();
        }

        private async Task AddSections()
        {
            showConfigIniKeys = false;
            NonMandatorySections = GetNonMandatorySections();

            await modal.ShowAsync();
        }

        private async Task ShowModal()
        {
            await modal.ShowAsync();
        }

        private async Task CloseModal()
        {
            await modal.HideAsync();
        }

        private List<ConfigIniSectionDTO> GetNonMandatorySections(string? sectionName = null)
        {
            if (sectionName is not null)
            {
                return TstConfigIniViewModel.ConfigIniAllSections!
                    .Where(s => s.SectionName.Equals(sectionName, StringComparison.CurrentCultureIgnoreCase))
                    .ToList();
            }
            else
            {
             return TstConfigIniViewModel.ConfigIniAllSections!
                    .Where(s => s.SectionFields.All(f => !f.IsMandatory))
                    .Select(s => new ConfigIniSectionDTO
                    {
                        SectionName = s.SectionName,
                        SectionFields = s.SectionFields,
                        IsChecked = TstConfigIniViewModel.ConfigIniSectionsOfUser!.Any(u => u.SectionName == s.SectionName && u.ChildrenHasValue)
                    })
                    .ToList();

            }
        }

        private List<ConfigIniSectionDTO> GetNonMandatorySectionsOfUser()
        {
            if (TstConfigIniViewModel.ConfigIniSectionsOfUser is null)
            {
                return new List<ConfigIniSectionDTO>();
            }


            return TstConfigIniViewModel.ConfigIniSectionsOfUser
           .Where(s => s.SectionFields.All(f => !f.IsMandatory) && s.SectionFields.Any(f => f.IsInCustmerXml))
           .Select(s => new ConfigIniSectionDTO
           {
               SectionName = s.SectionName,
               SectionFields = s.SectionFields
           }).ToList();

        }


        // Méthode de validation des champs
        private bool ValidateForm(List<ConfigIniSectionDTO> sections)
        {
            bool allValid = true;

            foreach (var section in sections)
            {
                bool sectionHasError = false;

                foreach (var field in section.SectionFields)
                {
                    if (field.IsMandatory && string.IsNullOrWhiteSpace(field.FieldValue))
                    {
                        field.IsValid = false;
                        sectionHasError = true;
                        allValid = false;
                    }
                    else
                    {
                        field.IsValid = true;
                    }
                }

                if (sectionHasError)
                {
                    section.IsOpen = true;
                }
            }

            // Mise à jour de la liste des sections obligatoires
            mandatorySections = sections
                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                .OrderBy(s => s.SectionName)
                .ToList();

            FormClass = "was-validated";

            return allValid;
        }

        private void ShowValidationErrorToast(string message)
        {
            ToastMessage toastMessage = new ToastMessage
            {
                Type = ToastType.Danger,
                AutoHide = true,
                Title = Localizer["toast_message_title_form_validation"],
                Message = message
            };

            toastMessages.Add(toastMessage);
        }

        private async Task SaveFormAsync()
        {
            // Assurez-vous que toutes les sections obligatoires sont incluses
            var missingMandatorySections = TstConfigIniViewModel.ConfigIniAllSections?
                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                .Where(s => TstConfigIniViewModel.ConfigIniSectionsOfUser == null || !TstConfigIniViewModel.ConfigIniSectionsOfUser.Any(u => u.SectionName == s.SectionName))
                .OrderBy(s => s.SectionName)
                .ToList();

            // Ajouter les sections obligatoires manquantes
            if (missingMandatorySections != null)
            {
                foreach (var section in missingMandatorySections)
                {
                    TstConfigIniViewModel.ConfigIniSectionsOfUser?.Add(new ConfigIniSectionDTO
                    {
                        SectionName = section.SectionName,
                        SectionFields = section.SectionFields,
                        IsOpen = false
                    });
                }
            }

            if (TstConfigIniViewModel.ConfigIniSectionsOfUser != null && ValidateForm(TstConfigIniViewModel.ConfigIniSectionsOfUser))
            {
                try
                {
                    await ThemisSupportToolsManager.SauvegarderConfigIniAsync(StructureId.Value, TstConfigIniViewModel);
                    toastMessages.Add(new ToastMessage
                    {
                        Type = ToastType.Success,
                        AutoHide = true,
                        Title = Localizer["toast_message_title_success"],
                        Message = Localizer["toast_message_form_submitted"]
                    });
                    NavigationManager.NavigateTo(Navigation.BaseUri);
                }
                catch (Exception ex)
                {
                    toastMessages.Add(new ToastMessage
                    {
                        Type = ToastType.Danger,
                        AutoHide = true,
                        Title = Localizer["toast_message_title_error"],
                        Message = Localizer["toast_message_form_submission_failed", ex.Message]
                    });
                }
            }
            else
            {
                ShowValidationErrorToast(Localizer["toast_message_form_validation_failed"]);
                StateHasChanged();
            }
        }
    }
}