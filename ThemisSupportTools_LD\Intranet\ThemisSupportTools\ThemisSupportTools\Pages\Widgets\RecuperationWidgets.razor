﻿@page "/recuperationwidgets"
@inject IJSRuntime jsRuntime


<h1 id="title">Récupération Widgets </h1>

<div id="opinionOrderFormWrapper"></div>

@code {

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        // invoke script loader
        Console.WriteLine("Loading jQuery");
        // note the scripts will only load once
        await jsRuntime.InvokeVoidAsync("loadScript", "https://code.jquery.com/jquery-3.4.1.js");
        await jsRuntime.InvokeVoidAsync("loadScript", "https://test2.themisweb.fr/widgets/customers/v1/widget-js");
        await jsRuntime.InvokeVoidAsync("loadScript", "js/test_widget.js");

        await base.OnAfterRenderAsync(firstRender);
    }


}
