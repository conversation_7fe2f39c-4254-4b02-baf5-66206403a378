﻿

    @if (!string.IsNullOrWhiteSpace(PlaceHolder))
    {
        <option value="">@PlaceHolder</option>
    }

    <select>
        @foreach (var item in ListItems)
        {
            <option value="@item" @onchange="ChangeEnvironment">@item</option>
        }
    </select>


@code {
    [Parameter] public List<string> ListItems { get; set; } = new();
    [Parameter] public string PlaceHolder { get; set; } = string.Empty;

    [Parameter] public string CurrentEnvionnement { get; set; } = string.Empty;

    //changement d'environnement 
    public void ChangeEnvironment()
    {
        
    }
}
