﻿using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;
using System.Threading;
using System.Threading.Tasks;

namespace MiseEnVentes.Interfaces
{
    public interface IApplicationDbContext
    {
        DbSet<Intervenant> Intervenants { get; }
        DbSet<MiseEnVenteNew> MisesEnVenteNew { get; }
        DbSet<MiseEnVenteIntervenant> MiseEnVenteIntervenants { get; }
        DbSet<Plateforme> Plateformes { get; }
        DbSet<MiseEnVentePlateforme> MiseEnVentePlateformes { get; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
}
