

Declare @TblFunctions table (FunctionId int, FunctionName varchar(250) )
[FOREACH.INSERTINTO]
/*
insert into  @TblFunctions values (8	,'Input_Session_Date_Picker')
insert into  @TblFunctions values (9	,'Select_Events_Genre_List_Multiple')
insert into  @TblFunctions values (10	,'Select_Events_Subgenre_List_Multiple')
insert into  @TblFunctions values (11	,'Select_Events_List_Multiple')
insert into  @TblFunctions values (12	,'Input_Session_Filling_Min')
insert into  @TblFunctions values (13	,'Input_Session_Filling_Max')
insert into  @TblFunctions values (16	,'Input_Items_Max_To_Display')

declare @pBlockEmplacement int = 191
declare @pLangCode varchar(5) = 'fr'
*/




declare @langue_id int = (select langue_id from langue where langue_code = @pLangCode)



Declare @TblDispoManifs table (Manif_Nom varchar(250)
								, Manif_id int 
								, Manif_Groupe_id int
								, Manif_Groupe_nom varchar(250)
								, Groupe_Genre_id  int 
								, Groupe_genre_nom varchar(250)
								, Genre_id  int 
								, Genre_nom varchar(250)
								, DateMin DateTime
								, DateMax Datetime
								, PlacesDispo decimal(18,10) 
								, PlacesTotal decimal(18,10)
								)



declare @BlockFunction_ID int 
Declare @SQL varchar(max)

Set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Input_Items_Max_To_Display')
Declare @NbTop int = ISNULL((select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ), 0)  --- Par défaut 8, mais pourra être changé

declare @ProfilAcheteurId int =0 --- Normalement, on ne gère pas le profil d'acheteur, mais à penser. donc 0 par défaut

Set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Input_Session_Filling_Min')
Declare @FiltreRemplissageMin int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 
Set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Input_Session_Filling_Max')
Declare @FiltreRemplissageMax int = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) --- Filtre à définir à l'appel du SQL 


Set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Input_Session_Date_Picker')
Declare @FiltreDatesSeance varchar(50) = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID =@BlockFunction_ID and BlockEmplacement_ID= @pBlockEmplacement ) -- les données de filtres brutes ( 04/04/2023 - 30/05/2030 )
Declare @FiltreDateSeanceDeb datetime --- la date deb 
Declare @FiltreDateSeancefin datetime --- la date fin

--- Attention, chaque filtre est additionnel

set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Select_Events_List_Multiple')
declare @FiltreManif varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																			where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )  --'1;2;3;4;7;8;9;10;11;12;13;14' --- Filtre à définir à l'appel du SQL  Select_Events_List_Multiple

set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Select_Events_Genre_List_Multiple')
Declare @FiltreGroupeGenre varchar(max) =  (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																		where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2;3;4' --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Groupe_genre = Genre Select_Events_Genre_List_Multiple

set @BlockFunction_ID = (select FunctionId from @TblFunctions where FunctionName = 'Select_Events_Subgenre_List_Multiple')
Declare @FiltreGenre Varchar(max) = (select BlockcUserConfig_Value from HomeModular_BlockUserConfig 
																		where BlockFunction_ID in (@BlockFunction_ID) and BlockEmplacement_ID= @pBlockEmplacement )---'1;2' --- --- Filtre à définir à l'appel du SQL ---- Sur Thémis Manifestation_Genre = Sous_Genre 
---- 

---- ****** Debut des requêtes ********* 
----
	---- Sélection des filtres
----

--- Filtres date deb séance / Date Fin séance
if  @FiltreDatesSeance > ''
	begin
	set @FiltreDateSeanceDeb = (left(@FiltreDatesSeance,10))+' ' + '00:00:00'
	--select @FiltreDateSeanceDeb
	set @FiltreDateSeancefin = (right(@FiltreDatesSeance,10))+' ' + '00:00:00'
	--select @FiltreDateSeancefin
	end
if  @FiltreDatesSeance = ''
	begin
		set @FiltreDateSeanceDeb = '01/01/1900'	
		set @FiltreDateSeancefin = '31/12/2199'
	end
---

--- Manifs 
declare @TmpFiltreManif table (Val int) 
if @FiltreManif = '' 
	begin 
		Set @SQL = 'Select Manifestation_id from Manifestation where manifestation_id > 999999 ' 		
	end 
if @FiltreManif > '' 
	begin 
		Set @SQL = 'Select Manifestation_id  from Manifestation  where Manifestation_id in (' + replace(@FiltreManif,';',',') +  ') ' 
	end 
if @FiltreManif = 'ALL' --- Pas de filtre
	begin 
		Set @SQL = 'Select Manifestation_id  from Manifestation  ' 
	end 
insert into @TmpFiltreManif exec (@SQL) 


--- Groupe Genre temp prenant en compte 0 
declare @TmpGroupeGenre table (Val int , Nom Varchar(250)) 
insert into @TmpGroupeGenre Select id , nom from manifestation_groupe_genre
insert into @TmpGroupeGenre values(0,'')

--- Filtre Groupe Genre 
declare @TmpFiltreGroupeGenre table (Val int) 
if @FiltreGroupeGenre = '' 
	begin 
		Set @SQL = 'Select id from manifestation_groupe_genre where id > 999999 ' 		
	end 
if @FiltreGroupeGenre > '' 
	begin 
		Set @SQL = 'Select id  from manifestation_groupe_genre  where id in (' + replace(@FiltreGroupeGenre,';',',') +  ') ' 
	end 
if @FiltreGroupeGenre = 'ALL' --- Pas de filtre
	begin 
		Set @SQL = 'Select id from manifestation_groupe_genre  ' 
	end 
insert into @TmpFiltreGroupeGenre exec (@SQL) 

--- Filtre Groupe Genre 
declare @TmpFiltreGenre table (Val int) 
if @FiltreGenre = '' 
	begin 
		Set @SQL = 'Select id from manifestation_genre where id > 999999 ' 		
	end 
if @FiltreGenre > '' 
	begin 
		Set @SQL = 'Select id  from manifestation_genre  where id in (' + replace(@FiltreGenre,';',',') +  ') ' 
	end 
if @FiltreGenre = 'ALL' --- Pas de filtre
	begin 
		Set @SQL = 'Select id from manifestation_genre  ' 
	end 
insert into @TmpFiltreGenre exec (@SQL) 
--

--- Genre temp prenant en compte 0 
declare @TmpGenre table (Val int , Nom Varchar(250),Groupe_id int) 
insert into @TmpGenre Select id ,nom, groupe_id from manifestation_genre
insert into @TmpGenre values(0,'',0)

----
	---- FIN Sélection des filtres
----

----- *************************************************************

----
	---- Génération de la requête --> Alimentation de la table temp finale
----

if @ProfilAcheteurId = 0 ---ATTENTION Filtres aditionnels, d'où le UNION
	begin
		insert into @TblDispoManifs
		select m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0) as 'Groupe_Genre_ID'
		,isnull(mgg.nom,'') as 'Groupe_Genre'
		,isnull(g.val,0) as 'Genre_ID'
		,isnull(g.nom,'') as 'Genre'
		,(select min(s1.seance_date_deb) from seance s1 where s1.manifestation_id = gp.manif_id) as 'Date_Deb'
		,(select max(s2.seance_date_deb) from seance s2 where s2.manifestation_id = gp.manif_id) as 'Date_fin'
		,sum (gp.dispo)
		,sum(gp.placestotal)
		from 
		gestion_place gp 
		inner join seance s on s.seance_Id = gp.seance_id		
		inner join manifestation m on m.manifestation_id = s.manifestation_id 
		left  join GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
		inner join manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		inner join @TmpGenre g on g.val = m.ID_genre
		inner join @TmpGroupeGenre mgg on mgg.val = g.Groupe_id
		inner join lieu l on l.lieu_id = s.lieu_id
		where dispo>0 and isvalide=1 
		and s.seance_date_deb >= getdate() 
		and isContrainteIdentite =0 
		and gp.formule_id is null 
		and gp.gestion_place_parent_id is null 
		and gp.dispo > 0 
		and gp.placesTotal > 0
		and (mgg.val in (select val from @TmpFiltreGroupeGenre)) 
		and s.seance_date_deb between @FiltreDateSeanceDeb and @FiltreDateSeancefin
		group by 
		m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0)
		,isnull(mgg.nom,'')
		,isnull(g.val,0)
		,isnull(g.nom,'') 	
		
		UNION
		select m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0) as 'Groupe_Genre_ID'
		,isnull(mgg.nom,'') as 'Groupe_Genre'
		,isnull(g.val,0) as 'Genre_ID'
		,isnull(g.nom,'') as 'Genre'
		,(select min(s1.seance_date_deb) from seance s1 where s1.manifestation_id = gp.manif_id) as 'Date_Deb'
		,(select max(s2.seance_date_deb) from seance s2 where s2.manifestation_id = gp.manif_id) as 'Date_fin'
		,sum (gp.dispo)
		,sum(gp.placestotal) 
		from 
		gestion_place gp 
		inner join seance s on s.seance_Id = gp.seance_id
		inner join manifestation m on m.manifestation_id = s.manifestation_id
		left  join GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
		inner join manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		inner join @TmpGenre g on g.val = m.ID_genre
		inner join @TmpGroupeGenre mgg on mgg.val = g.Groupe_id
		inner join lieu l on l.lieu_id = s.lieu_id
		where dispo>0 and isvalide=1 
		and s.seance_date_deb >= getdate() 
		and isContrainteIdentite =0 
		and gp.formule_id is null 
		and gp.gestion_place_parent_id is null 
		and gp.dispo > 0 
		and gp.placesTotal > 0
		and (g.val in (select val from @TmpFiltreGenre))
		and s.seance_date_deb between @FiltreDateSeanceDeb and @FiltreDateSeancefin
		group by 
		m.manifestation_nom
		, gp.manif_id
		, mg.manif_groupe_id 
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0)
		,isnull(mgg.nom,'')
		,isnull(g.val,0)
		,isnull(g.nom,'') 	
		
		UNION
		select m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0) as 'Groupe_Genre_ID'
		,isnull(mgg.nom,'') as 'Groupe_Genre'
		,isnull(g.val,0) as 'Genre_ID'
		,isnull(g.nom,'') as 'Genre'		
		,(select min(s1.seance_date_deb) from seance s1 where s1.manifestation_id = gp.manif_id) as 'Date_Deb'
		,(select max(s2.seance_date_deb) from seance s2 where s2.manifestation_id = gp.manif_id) as 'Date_fin'
		,sum (gp.dispo)
		,sum(gp.placestotal) 
		from 
		gestion_place gp 
		inner join seance s on s.seance_Id = gp.seance_id
		inner join manifestation m on m.manifestation_id = s.manifestation_id 
		left  join GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
		inner join manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		inner join @TmpGenre g on g.val = m.ID_genre
		inner join @TmpGroupeGenre mgg on mgg.val = g.Groupe_id
		inner join lieu l on l.lieu_id = s.lieu_id
		where dispo>0 and isvalide=1 
		and s.seance_date_deb >= getdate() 
		and isContrainteIdentite =0 
		and gp.formule_id is null 
		and gp.gestion_place_parent_id is null 
		and gp.dispo > 0 
		and gp.placesTotal > 0
		and m.manifestation_id in (select Val from @TmpFiltreManif)
		and s.seance_date_deb between @FiltreDateSeanceDeb and @FiltreDateSeancefin			
		group by 
		m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0)
		,isnull(mgg.nom,'')
		,isnull(g.val,0)
		,isnull(g.nom,'')
		
	end
if @ProfilAcheteurId > 0 ---- A modifier en fonction des paramètres précédents si la fonction est activée
	begin
		insert into @TblDispoManifs
		select m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0) as 'Groupe_Genre_ID'
		,isnull(mgg.nom,'') as 'Groupe_Genre'
		,isnull(g.val,0) as 'Genre_ID'
		,isnull(g.nom,'') as 'Genre'		
		,(select min(s1.seance_date_deb) from seance s1 where s1.manifestation_id = gp.manif_id) as 'Date_Deb'
		,(select max(s2.seance_date_deb) from seance s2 where s2.manifestation_id = gp.manif_id) as 'Date_fin'
		,sum (gp.dispo)
		,sum(gp.placestotal) 	
		from 
		gestion_place gp 
		inner join offre_gestion_place ogp on gp.gestion_place_id = ogp.gestion_place_id
		inner join offre o on o.offre_id = ogp.offre_id
		inner join offre_profil_acheteur opa on opa.offre_id = o.offre_id and opa.profil_acheteur_id =@ProfilAcheteurId  
		inner join seance s on s.seance_Id = gp.seance_id
		inner join manifestation m on m.manifestation_id = s.manifestation_id 
		left  join GP_MANIFESTATION gpm on  gpm.manifestation_id = m.manifestation_id 
		inner join manifestation_groupe mg on mg.manif_groupe_id = m.manifestation_groupe_id 
		inner join @TmpGenre g on g.val = m.ID_genre
		inner join @TmpGroupeGenre mgg on mgg.val = g.Groupe_id
		inner join lieu l on l.lieu_id = s.lieu_id
		where dispo>0 and isvalide=1 
		and s.seance_date_deb >= getdate() 
		and isContrainteIdentite =1 
		and gp.formule_id is null 
		and gp.gestion_place_parent_id is null 
		and gp.dispo > 0 
		and gp.placesTotal > 0
		and m.manifestation_id in (select Val from @TmpFiltreManif)
		and s.seance_date_deb between @FiltreDateSeanceDeb and @FiltreDateSeancefin
		group by 
		m.manifestation_nom
		, gp.manif_id 
		, mg.manif_groupe_id
		, mg.manif_groupe_nom
		,isnull(mgg.Val,0)
		,isnull(mgg.nom,'')
		,isnull(g.val,0)
		,isnull(g.nom,'') 	
		
	end 
----
	---- FIN Génération de la requête --> Alimentation de la table temp finale
----

---- ******************************************************************************

----
	---- Requête finale 
----

	select top (@NbTop) 
	isnull(trad_manif.manifestation_nom,  Manif_Nom) as Manif_Nom
	, manif_id 
	, Manif_Groupe_id
	, Manif_Groupe_nom 
	, Groupe_genre_id
	, isnull(trad_manif_genre.nom,  Groupe_genre_nom) as Groupe_genre_nom    --- Sur Thémis Manifestation_Groupe_genre = Genre
	, Genre_id
	, isnull(trad_manif_sous_genre.nom,  Genre_nom) as Genre_nom  ---- Sur Thémis Manifestation_Genre = Sous_Genre
	, DateMin as 'Seances_Date_Deb'
	, DateMax as 'Seances_Date_Fin'
	
	--, PlacesDispo as 'Dispos' ----- Pour vérifier le calcul des dispos
	--, PlacesTotal as 'Places_Total'
	--, PlacesTotal-PlacesDispo as 'Places_Prises'
	--, round(((PlacesTotal-PlacesDispo) / PlacesTotal *100),2) as 'Taux_Remplissage'
	from @TblDispoManifs dispM
	LEFT JOIN traduction_manifestation trad_manif on dispM.Manif_id = trad_manif.manifestation_id and trad_manif.langue_id = @langue_id
	Left JOIN traduction_manifestation_groupe_genre trad_manif_genre on trad_manif_genre.id = dispM.Groupe_Genre_id and trad_manif_genre.langue_id = @langue_id
	Left JOIN traduction_manifestation_genre trad_manif_sous_genre on trad_manif_sous_genre.id = dispM.Genre_id and trad_manif_sous_genre.langue_id = @langue_id
	where round(((PlacesTotal-PlacesDispo) / PlacesTotal *100),2) between @FiltreRemplissageMin and @FiltreRemplissageMax
	group by isnull(trad_manif.manifestation_nom,  Manif_Nom),Manif_Groupe_id,Manif_id,Manif_Groupe_nom,Groupe_genre_id,isnull(trad_manif_genre.nom,  Groupe_genre_nom),
	Genre_id,isnull(trad_manif_sous_genre.nom,  Genre_nom),DateMin,DateMax
	--,PlacesDispo,PlacesTotal  ----- Pour vérifier le calcul des dispos
	
	order by newid()

----
	---- FIN Requête finale 
----


--select *from gestion_place gp 
--inner join seance s on s.seance_id = gp.seance_id 

--where    s.seance_date_deb >= getdate()  and manif_id =1  
--and gp.isContrainteIdentite =0 ---> tout public
--and gp.formule_id is null and gp.gestion_place_parent_id is null

--and gp.type_tarif_id > 0
