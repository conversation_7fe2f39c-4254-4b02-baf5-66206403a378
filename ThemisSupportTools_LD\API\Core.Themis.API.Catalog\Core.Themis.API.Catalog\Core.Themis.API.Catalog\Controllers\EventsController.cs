﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Net;
using System.Threading.Tasks;
using Core.Themis.Libraries.BLL.Interfaces;

namespace Core.Themis.API.Catalog.Controllers
{
    [ApiController]
    public class EventsController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly ISessionManager _sessionManager;
        private readonly IEventManager _eventManager;

        private static readonly RodrigueNLogger Logger = new();

        public EventsController(
            IConfiguration configuration,
            IMemoryCache memoryCache, 
            IApiUtilities apiUtilities,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary, 
            ISessionManager sessionManager,
            IEventManager eventManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _sessionManager = sessionManager;
            _eventManager = eventManager;
        }


        /// <summary>
        /// Ramène les prochaines manifs à venir sur un temps donné
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="interval">en minutes, evenements de getdate()-1 heure jusqu'à {interval} minutes</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]
        [Route("api/{structureId}/events/futuresto/{interval}/{langCode}")]
        public async Task<IActionResult> LoadFuturesEventsSessions(int structureId, string langCode, int interval)
        {
            try
            {
                Logger.Debug(structureId, $"EventsListOfToday({structureId},{langCode}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var eventsList = await _eventManager.LoadFuturesEventsSessionsAsync(structureId, interval, langCode)
                                                    .ConfigureAwait(false);

                Logger.Debug(structureId, $"EventsListOfToday({structureId},{langCode}) ok {eventsList.Count} events to return");

                return Ok(eventsList);

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"EventsListOfToday({structureId},{langCode}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Renseigne infos de manif et séance 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(EventDTO))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 10)]
        [HttpGet]
        [Route("api/{structureId}/events/{eventid}/{sessionid}/{langCode}")]
        public async Task<IActionResult> LoadEventsSessions(int structureId, string langCode, int eventid, int sessionid)
        {
            try
            {
                Logger.Debug(structureId, $"LoadEventsSessions({structureId},{langCode}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var listeventE = _eventManager.Load(structureId, new List<int> { eventid }, langCode);

                if (listeventE != null && listeventE.Count == 1)
                {
                    var sessions = _sessionManager.Load(structureId, new List<int> { sessionid }, langCode);
                    listeventE[0].ListSessions = sessions;

                    Logger.Debug(structureId, $"LoadEventsSessions({structureId},{langCode}) ok {listeventE.Count} events to return");

                    return Ok(listeventE[0]);
                }
                else
                {
                    Logger.Error(structureId, $"LoadEventsSessions({structureId},{langCode}) can't retrieve event");
                    return Problem("can't retrieve event", null, StatusCodes.Status500InternalServerError);
                }
            }

            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadEventsSessions({structureId},{langCode}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
