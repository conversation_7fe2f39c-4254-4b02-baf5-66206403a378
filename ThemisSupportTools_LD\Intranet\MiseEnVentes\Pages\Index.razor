﻿@page "/admin"
@inherits IndexBase
@using Microsoft.EntityFrameworkCore;
@using MiseEnVentes.Models;
@using MiseEnVentes.Shared;
@using MudBlazor
@using System.ComponentModel.DataAnnotations
@using Blazored.Typeahead





<div class="header-actions">
    <h1>Gérer les mises en ventes</h1>
    <button class="btn btn-primary" @onclick="TogglePopup" id="createNewMiseEnVente">
        Créer une nouvelle mise en vente <i class="fas fa-plus-circle" aria-hidden="true"></i>
    </button>
</div>


<div id="filtre" class="d-flex align-items-center gap-3 flex-wrap mb-4">
    <i class="fas fa-filter text-primary fs-4"></i>

    <!-- Filtre 1 : Intervenants -->
    <select class="form-select w-auto" @onchange="OnIntervenantChanged" aria-label="Filtrer par intervenant">
        <option value="">Tous les intervenants</option>
        @if (intervenants != null)
        {
        @foreach (var i in intervenants)
            {
        <option value="@i.IdIntervenant">@i.IntervenantName</option>
            }
        }
    </select>



    <!-- Filtre 2 : Plateformes -->
    <select class="form-select w-auto" @onchange="OnPlateformeChanged" aria-label="Filtrer par Plateformes">
        <option value="">Toutes les Plateformes</option>
        @if (plateformees != null)
        {
        @foreach (var i in plateformees)
            {
        <option value="@i.IdPlateforme">@i.PlateformeName</option>
            }
        }
    </select>



    <!-- Filtre 3 : Structures -->
    <input list="structures" class="form-control" style="width: 600px;" value="@selectedStructureName" @onchange="OnStructureChanged"
           placeholder="Rechercher une structure..." />

    <datalist id="structures">
        @foreach (var structure in AccountBasesNum.Values)
        {
        <option value="@($"{structure.Name} - {structure.jav_IDStructure}")">@structure.Name - @structure.jav_IDStructure</option>
        }
    </datalist>




    <!-- Filtre 4 : Date -->
    <select class="form-select w-auto" aria-label="Filtrer par date" @onchange="OnDateFilterChanged">
        <option value="0" selected>Aujourd'hui et +</option>
        @for (var year = DateTime.Now.Year - 5; year <= DateTime.Now.Year + 1; year++)
        {
        <option value="@year">@year</option>
        }
    </select>
</div>



<!-- Modal pour ajouter une mise en vente -->
@if (showPopup)
{
<div class="modal show d-block" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <EditForm Model="@formModel" OnValidSubmit="HandleValidSubmit">
                <div class="modal-header">
                    <h5 class="modal-title">Nouvelle mise en vente</h5>
                    <button type="button" class="btn-close" @onclick="TogglePopup"></button>
                </div>

                <div class="modal-body">
                    <DataAnnotationsValidator />

                    <div class="mb-3">
                        <label>Date :</label>
                        <InputDate class="form-control" @bind-Value="formModel.Date" />
                    </div>

                    <div class="mb-3">
                        <label>Heure :</label>
                        <InputText type="time" class="form-control" @bind-Value="formModel.Heure" />
                        <ValidationMessage For="@(() => formModel.Heure)" />
                    </div>

                    <div class="mb-3">
                        <label>Plateformes :</label>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 5px;">
                            @if (plateformees != null)
                                {
                            @foreach (var p in plateformees)
                                    {
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="<EMAIL>"
                                       checked="@formModel.Plateformes.Contains(p.IdPlateforme)"
                                       @onchange="e => OnPlateformeCheckedChanged(p.IdPlateforme, e.Value)">
                                <label class="form-check-label" for="<EMAIL>">@p.PlateformeName</label>
                            </div>
                                    }
                                }
                        </div>
                    </div>

                    <div class="mb-3">
                        <label>Structure :</label>
                        <input list="structures" class="form-control" @bind-value="formModel.Structure" placeholder="Sélectionnez ou écrivez une structure">
                        <datalist id="structures">
                            @foreach (var structure in AccountBasesNum.Values)
                                {
                            <option value="@($"{structure.Name} - {structure.jav_IDStructure}")">@structure.Name - @structure.jav_IDStructure</option>
                                }
                        </datalist>
                        <ValidationMessage For="@(() => formModel.Structure)" />
                    </div>

                    <div class="mb-3">
                        <label>Intervenants :</label>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 5px;">
                            @if (intervenants != null)
                                {
                            @foreach (var i in intervenants)
                                    {
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="<EMAIL>"
                                       checked="@formModel.Intervenants.Contains(i.IdIntervenant)"
                                       @onchange="e => OnIntervenantCheckedChanged(i.IdIntervenant, e.Value)">
                                <label class="form-check-label" for="<EMAIL>">@i.IntervenantName</label>
                            </div>
                                    }
                                }
                        </div>
                    </div>

                    <div class="form-check">
                        <InputCheckbox class="form-check-input" @bind-Value="formModel.Mail" />
                        <label class="form-check-label">Mail</label>
                    </div>

                    <div class="form-check mb-3">
                        <InputCheckbox class="form-check-input" @bind-Value="formModel.Ticket" />
                        <label class="form-check-label">Ticket</label>
                    </div>

                    <div class="mb-3">
                        <label>Affluence de la mise en vente :</label>
                        <InputSelect class="form-control" @bind-Value="formModel.Importance">
                            <option value="@ImportanceNiveau.Normal">@ImportanceNiveau.Normal</option>
                            <option value="@ImportanceNiveau.Moyen">@ImportanceNiveau.Moyen</option>
                            <option value="@ImportanceNiveau.Eleve">@ImportanceNiveau.Eleve</option>
                        </InputSelect>
                    </div>


                    <div class="mb-3">
                        <label>Commentaire :</label>
                        <InputTextArea class="form-control" @bind-Value="formModel.Commentaire" maxlength="200" />
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-success" type="submit">Valider</button>
                    <button class="btn btn-danger" type="button" @onclick="TogglePopup">Annuler</button>
                </div>
            </EditForm>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
}






<!-- Toast pour prévenir de l'action effectuer -->

@if (showToast)
{
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div class="toast show toast-custom" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Succès</strong>
            <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
        </div>
        <div class="toast-body">
            La mise en vente a été ajoutée avec succès.
        </div>
    </div>
</div>
}



<!-- Modal pour modifier une mise en vente -->

@if (showEditModal && selectedVente != null)
{
<div class="modal show d-block" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <EditForm Model="editModel" OnValidSubmit="SubmitEdit">
                <div class="modal-header">
                    <h5 class="modal-title">Modifier la vente</h5>
                    <button type="button" class="btn-close" @onclick="CloseEditModal"></button>
                </div>

                <div class="modal-body">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label class="form-label">Date</label>
                        <InputDate class="form-control" @bind-Value="editModel.Date" />
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Heure</label>
                        <InputText Type="Time" class="form-control" @bind-Value="editModel.Heure" />
                        <ValidationMessage For="@(() => editModel.Heure)" />
                    </div>

                    <div class="mb-3">
                        <label>Plateformes :</label>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 5px;">
                            @if (plateformees != null)
                                {
                            @foreach (var p in plateformees)
                                    {
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="<EMAIL>"
                                       checked="@editModel.Plateformes.Contains(p.IdPlateforme)"
                                       @onchange="e => OnEditPlateformeCheckedChanged(p.IdPlateforme, e.Value)">
                                <label class="form-check-label" for="<EMAIL>">@p.PlateformeName</label>
                            </div>
                                    }
                                }
                        </div>
                    </div>

                    <div class="mb-3">
                        <label>Structure :</label>
                        <input list="structures" class="form-control" @bind-value="editModel.Structure" placeholder="Sélectionnez ou écrivez une structure">
                        <datalist id="structures">
                            @foreach (var structure in AccountBasesNum.Values)
                                {
                            <option value="@($"{structure.Name} - {structure.jav_IDStructure}")">@structure.Name - @structure.jav_IDStructure</option>
                                }
                        </datalist>
                    </div>

                    <div class="mb-3">
                        <label>Intervenants :</label>
                        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 5px;">
                            @if (intervenants != null)
                                {
                            @foreach (var i in intervenants)
                                    {
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       id="<EMAIL>"
                                       checked="@editModel.Intervenants.Contains(i.IdIntervenant)"
                                       @onchange="e => OnEditIntervenantCheckedChanged(i.IdIntervenant, e.Value)">
                                <label class="form-check-label" for="<EMAIL>">@i.IntervenantName</label>
                            </div>
                                    }
                                }
                        </div>
                    </div>

                    <div class="form-check">
                        <InputCheckbox class="form-check-input" @bind-Value="editModel.Mail" />
                        <label class="form-check-label">Mail</label>
                    </div>

                    <div class="form-check">
                        <InputCheckbox class="form-check-input" @bind-Value="editModel.Ticket" />
                        <label class="form-check-label">Ticket</label>
                    </div>

                    <div class="mb-3">
                        <label>Affluence de la mise en vente :</label>
                        <InputSelect class="form-control" @bind-Value="editModel.Importance">
                            <option value="@ImportanceNiveau.Normal">@ImportanceNiveau.Normal</option>
                            <option value="@ImportanceNiveau.Moyen">@ImportanceNiveau.Moyen</option>
                            <option value="@ImportanceNiveau.Eleve">@ImportanceNiveau.Eleve</option>
                        </InputSelect>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Commentaire</label>
                        <InputText class="form-control" @bind-Value="editModel.Commentaire" maxlength="200" />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Enregistrer</button>
                    <button type="button" class="btn btn-secondary" @onclick="CloseEditModal">Annuler</button>
                </div>
            </EditForm>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
}

<!-- Toast pour prévenir de l'action effectuer -->

@if (showEditToast)
{
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div class="toast show toast-custom" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Succès</strong>
            <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
        </div>
        <div class="toast-body">
            La mise en vente a été modifier avec succès.
        </div>
    </div>
</div>
}

<!-- Modal pour supprimer une mise en vente -->

@if (showDeleteModal)
{
<div class="modal show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" @onclick="CancelDelete"></button>
            </div>
            <div class="modal-body">
                <p style="font-weight: bold; color: black;">Es-tu sûr de vouloir supprimer cette mise en vente ?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" @onclick="CancelDelete">Annuler</button>
                <button class="btn btn-danger" @onclick="DeleteVente">Supprimer</button>
            </div>
        </div>
    </div>
</div>
}


<!-- Toast pour prévenir de l'action effectuer -->

@if (showDeleteToast)
{
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div class="toast show toast-custom-delete" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Suppression</strong>
            <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
        </div>
        <div class="toast-body">
            La mise en vente a été supprimée avec succès.
        </div>
    </div>
</div>
}



<!-- Tableaux des mises en ventes  -->

@if (misesEnVente == null)
{
<p>Chargement des mises en vente...</p>
}
else if (!misesEnVente.Any())
{
<p>Aucune mise en vente trouvée.</p>
}
else
{
<table class="table table-bordered">
    <thead>
        <tr>
            <th style="border: 1px solid black;">Modifier</th>
            <th style="border: 1px solid black;">Date</th>
            <th style="border: 1px solid black;">Heure</th>
            <th style="border: 1px solid black;">Plateforme</th>
            <th style="border: 1px solid black;">Structure</th>
            <th style="border: 1px solid black;">Intervenant</th>
            <th style="border: 1px solid black;">Mail</th>
            <th style="border: 1px solid black;">Ticket</th>
            <th style="border: 1px solid black;">Commentaire</th>
            <th style="border: 1px solid black;">Affluence</th>
            <th style="border: 1px solid black;">Supprimer</th>
        </tr>
    </thead>

    <tbody>
        @foreach (var group in misesEnVente
              .Where(m => m.Date.HasValue)
              .OrderBy(m => m.Date)
              .GroupBy(m => new { m.Date.Value.Year, m.Date.Value.Month }))
            {

                var mois = new DateOnly(group.Key.Year, group.Key.Month, 1);
                string moisAffiche = mois.ToString("MMMM yyyy", new System.Globalization.CultureInfo("fr-FR"));

        <tr>
            <td colspan="20" style="background-color: #f0f0f0; font-weight: bold; border: 1px solid black;">
                @moisAffiche
            </td>
        </tr>

        @foreach (var vente in group.OrderBy(v => v.Date).ThenBy(v => v.Hour))
                {
        <tr class="@(vente.IdMiseEnVente == lastModifiedId ? "table-success" : "")">

            <td style="border: 1px solid black;">
                <button class="btn btn-sm btn-warning" @onclick="@(() => OpenEditModal(vente))">Modifier</button>
            </td>

            <td style="border: 1px solid black;">@vente.Date?.ToString("dd/MM/yyyy")</td>
            <td style="border: 1px solid black;">@vente.Hour?.ToString("HH\\:mm")</td>
            <td style="border: 1px solid black;">
                @foreach (var plat in vente.MiseEnVentePlateformes.Select(mp => mp.Plateforme))
                          {
                <div>@plat.PlateformeName</div>
                          }
            </td>

            <td style="border: 1px solid black;">
                @{
                              string nomStructure = "Inconnue";

                              if (Guid.TryParse(vente.StructureId, out Guid structureGuid)
                                  && AccountBasesNum.TryGetValue(structureGuid, out var account))
                              {
                                  nomStructure = $"{account.Name} - {account.jav_IDStructure}";
                              }
                }
                @nomStructure
            </td>

            <td style="border: 1px solid black;">
                @foreach (var inter in vente.MiseEnVenteIntervenants.Select(mvi => mvi.Intervenant))
                          {
                <div>@inter.IntervenantName</div>
                          }
            </td>

            <td>
                <i class="@($"fa-solid fa-envelope {(vente.NotifMail ?? false ? "text-success" : "text-lightgray")}")"
                   title="Outlook Ok"></i>
            </td>

            <td>
                <i class="@($"fa-solid fa-ticket {(vente.NotifTicket ?? false ? "text-success" : "text-lightgray")}")"
                   title="Ticket Oui"></i>
            </td>

            <td>@vente.Commentaire</td>

            <td>
                @if (vente.Importance != null)
                          {
                              var niveau = (ImportanceNiveau)vente.Importance;
                <span class="@niveau.GetBadgeClass()">
                    @niveau.GetLabel()
                </span>
                          }
                          else
                          {
                <span class="badge bg-secondary">Non défini</span>
                          }
            </td>

            <td>
                <button class="btn btn-sm btn-danger" @onclick="@(() => ConfirmDelete(vente))">Supprimer</button>
            </td>
        </tr>
                }
            }
    </tbody>
</table>
}





