{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DBTest": "Server=127.0.0.1;Database=test;Persist Security Info=True;User ID=root;Password=*"*""}, "Images": {"LogoRodrigueCouleurs": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Logo-Rodrigue-Horizontal-couleurs-CMJN-PNG-72-DPI.png", "PlusIcon": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**plus.png", "MoinsIcon": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**moins.png", "PasSatisfait": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Satisfaction_1_etoile.png", "PasTresSatisfait": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Satisfaction_2_etoiles.png", "Satisfait": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Satisfaction_3_etoiles.png", "AssezSatisfait": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Satisfaction_4_etoiles.png", "TresSatisfait": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**Satisfaction_5_etoiles.png", "EyeOn": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**EyeOn.png", "EyeOff": "C:**Users**<USER>**source**repos**Cri**wwwroot**img**EyeOff.png"}, "LienSite": {"Inscription": "https://localhost:7050/Inscription/Etape1/{token}", "NewPwd": "https://localhost:7050/Changement_MDP?token={token}", "redirectionConnexion": "https://localhost:7050/connexion"}, "Download": "https://localhost:7050/upload/"}