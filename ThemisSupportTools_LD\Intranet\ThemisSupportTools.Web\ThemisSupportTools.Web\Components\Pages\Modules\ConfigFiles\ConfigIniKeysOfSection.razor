﻿@inject IStringLocalizer<Resource> Localizer



<div class="col-12 p-2 border-3 border-start @BorderColor"
     data-parent="@SectionName">

    <Tooltip Title="@Localizer[Field.Comment]" Placement="TooltipPlacement.Top">
        <DynamicLabel Isrequired="@Field.IsMandatory" Comment="@Field.Comment" CssClass="@BorderColor">
            @Field.FieldName
        </DynamicLabel>
    </Tooltip>


    @if (Field.IsMultiple.HasValue && (bool)Field.IsMultiple)
    {

        if (!string.IsNullOrEmpty(Field.ConnectedToGroup))
        {
            var timestamp = Field.HtmlAttributes?.ContainsKey("data-update-timestamp") == true
                ? Field.HtmlAttributes["data-update-timestamp"].ToString()
                : "";
            var uniqueKey = $"{SectionName}_{Field.FieldName}_{timestamp}_{string.Join(",", Field.FieldMultipleValue ?? new string[0])}";

            <Select2 @key="@uniqueKey"
                     id="@($"{SectionName}_{Field.FieldName}")"
                     class="@($"form-control {BorderColor}")"
                     data-placeholder="Choisir option"
                     data-allow-clear="true"
                     multiple
                     data-dropdown-parent="@ModalId"
                     SelectOptionsItem="@SetConnectedToGroup(Field.ConnectedToGroup)"
                     Value="Field.FieldMultipleValue"
                     OnChangeValue="((string[] value) => ChangeMultipleSelectValueForSectionGroup(value, Field))">
            </Select2>
            <div class="invalid-feedback">@Field.MandatoryMessage</div>
        }
        else
        {
            <Select2 id="@($"{SectionName}_{Field.FieldName}")"
                     class="form-control"
                     data-placeholder="Choisir option"
                     data-allow-clear="true"
                     multiple
                     data-dropdown-parent="@ModalId"
                     SelectOptionsItem="Field.SelectData"
                     Value="Field.FieldMultipleValue"
                     OnChangeValue="((string[] value) => ChangeMultipleSelectValue(value, Field))">
            </Select2>
            <div class="invalid-feedback">@Field.MandatoryMessage</div>
        }

    }
    else
    {
        @if (Field.Type.ToLower() == TSTFieldTypeEnum.Select.ToString().ToLower())
        {


            bool showValueInSelect = !string.IsNullOrWhiteSpace(Field.TableFieldValue);

            <Select2 id="@($"{SectionName}_{Field.FieldName}")"
                     class="@($"form-control {BorderColor}")"
                     data-placeholder="Choisir option"
                     data-allow-clear="true"
                     data-dropdown-parent="@ModalId"
                     SelectOptionsItem="Field.SelectData"
                     Value="Field.FieldValue"
                     OnChangeValue="((string value) => ChangeSelecValue(value, Field))">
            </Select2>

            @*<div class="invalid-feedback">@Localizer["message_champ_obligatoire_xml"]</div>*@
            <div class="invalid-feedback">@Field.MandatoryMessage</div>
        }
        else
        {
            string cssClass = "form-control";
            if (Field.Type.ToLower() == TSTFieldTypeEnum.Checkbox.ToString().ToLower())
                cssClass = "form-check-input";

            <input @onchange="((e) => ChangeValue(e, Field))" value="@Field.FieldValue" placeholder="@KeyNotUsed" class="@cssClass @BorderColor" @attributes="Field.HtmlAttributes" />
            <div class="invalid-feedback ">@Field.MandatoryMessage</div>
        }
    }


</div>


