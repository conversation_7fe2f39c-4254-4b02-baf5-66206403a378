
--DECLARE @pOrderId INT = 86437
DECLARE @manifId int

DECLARE MY_CURSOR CURSOR 
LOCAL STATIC READ_ONLY FORWARD_ONLY
FOR 
	SELECT cl.manifestation_id
	FROM commande c
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	WHERE c.commande_id = @pOrderId AND type_ligne = 'DOS'

OPEN MY_CURSOR
FETCH NEXT FROM MY_CURSOR INTO @manifId
WHILE @@FETCH_STATUS = 0
BEGIN 
	Declare @sql NVARCHAR(MAX) = 
	'SELECT *
	FROM commande c
	INNER JOIN commande_ligne cl ON c.commande_id = cl.commande_id
	INNER JOIN identite i ON c.identite_id = i.identite_id
	LEFT JOIN recette rDeb
		ON cl.manifestation_id = rDeb.manifestation_id and rDeb.recette_id = 
			(select min(recette_id) from recette r2 where r2.entree_id = rDeb.entree_id and r2.dossier_id = rDeb.dossier_id and r2.seance_id = rDeb.seance_id)
		AND cl.seance_id = rDeb.seance_id
		AND cl.dossier_id = rDeb.dossier_id
	LEFT JOIN recette rFin
		ON cl.manifestation_id = rFin.manifestation_id and rFin.recette_id = 
			(select max(recette_id) from recette r2 where r2.entree_id = rDeb.entree_id and r2.dossier_id = rDeb.dossier_id and r2.seance_id = rDeb.seance_id)
		AND cl.seance_id = rFin.seance_id
		AND cl.dossier_id = rFin.dossier_id

	INNER JOIN entree_' + CAST(@manifId AS varchar) + ' e  ON cl.seance_id = e.seance_id  and rDeb.entree_id = e.entree_id and rFin.entree_id = e.entree_id
	INNER JOIN reference_lieu_physique rlp ON e.reference_unique_physique_id = rlp.ref_uniq_phy_id
	LEFT JOIN commande_infos ci ON c.commande_id = ci.commande_id
	LEFT JOIN Commande_Ligne_comp clp ON cl.commande_ligne_id = clp.commande_ligne_id
	WHERE c.commande_id = ' + CAST(@pOrderId AS varchar)

	EXEC(@sql)

	FETCH NEXT FROM MY_CURSOR INTO @manifId
END
CLOSE MY_CURSOR
DEALLOCATE MY_CURSOR
