﻿function scrollToControl(controlId) {
    document.querySelector(controlId).scrollIntoView({ behavior: "smooth", block: "center" })
}

function scrollToTop() {
    window.scroll({ top: 0, behavior: 'smooth' });
}

function scrollTo(elementId) {
    var element = document.getElementById(elementId);
    if (element != undefined) {
        element.scrollIntoView({
            behavior: 'smooth'
        });

    }
   
}
window.getSelectedValues = function (sel) {
    var results = []; 
    var i;
    for (i = 0; i < sel.options.length; i++) {
        if (sel.options[i].selected) {
            results[results.length] = sel.options[i].value;
        }
    }
    return results;

}