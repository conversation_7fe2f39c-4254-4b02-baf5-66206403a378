﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MiseEnVentes.Models
{
    public class MiseEnVenteIntervenant
    {
        [Key]
        public int IdMiseEnVenteIntervenant { get; set; }


        [ForeignKey("IdMiseEnVente")]
        public int IdMiseEnVente { get; set; }
        public MiseEnVenteNew MiseEnVente { get; set; } = null!;

        [ForeignKey("IdIntervenant")]
        public int IdIntervenant { get; set; }
        public Intervenant Intervenant { get; set; } = null!;
    }
}
