﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.Themis.API.Catalog.Handlers
{



    public class RolesAuthorizationHandler : AuthorizationHandler<RolesAuthorizationRequirement>, IAuthorizationHandler
    {
        private static readonly NLog.Logger Logger = NLog.LogManager.GetCurrentClassLogger();

        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
                                                       RolesAuthorizationRequirement requirement)
        {
            if (context.User == null || !context.User.Identity.IsAuthenticated)
            {

                Logger.Error("token absent ou invalide");

                context.Fail();
                return Task.CompletedTask;
            }

            var validRole = false;
            if (requirement.AllowedRoles == null ||
                requirement.AllowedRoles.Any() == false)
            {
                validRole = true;
            }
            else
            {
                List<string> listRolesDuToken = context.User.Claims.Where(m => m.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

                var claims = context.User.Claims;
                //var userName = claims.FirstOrDefault(c => c.Type == "name").Value;
                var rolesDemandes = requirement.AllowedRoles;

                validRole = rolesDemandes.Where(p => listRolesDuToken.Contains(p)).Any();

                //validRole = true;
                //validRole = new Users().GetUsers().Where(p => roles.Contains(p.Role) && p.UserName == userName).Any();
            }

            if (validRole)
            {
                context.Succeed(requirement);
            }
            else
            {
                Logger.Error("L'utilisateur n'a pas le bon role");

                context.Fail();
            }
            return Task.CompletedTask;
        }
    }
}
