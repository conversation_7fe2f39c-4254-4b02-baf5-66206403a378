/* checkAdhesionToConsumer.sql */
/*

declare @pIdentiteId int 
set @pIdentiteId = '621'

declare @padherentId int 
set @padherentId = '10991161270'

declare @pPriceId int
set @pPriceId = '5650025'

declare @pGpId int

*/
declare @PriceId int
declare @seanceId int, @manifId int

select @PriceId = type_tarif_id, @seanceId = seance_id, @manifId = manif_id from gestion_place WHERE gestion_place_id = @pGpId;

DECLARE @myidentite_id int
if (@padherentId != '0')
begin /* num adherent renseigné, on le check et on remplit @myidentite_id s'il est ok */
	declare @reversidentite_id int
	set @reversidentite_id = dbo.F_adhesion_num_reverse(@padherentId)
	if (@reversidentite_id is not null)
	BEGIN 
		set @myidentite_id = @reversidentite_id
	END 
	ELSE	
	BEGIN
		select 'KO:CANTRETRIEVEIDENTITYID' as ret
		--THROW 51000, 'cannot retrieve identite_id by this adhesion num !', 1;  
	END
END
ELSE
BEGIN
	set @myidentite_id = @pIdentiteId;
END


IF (@myidentite_id is not null and @myidentite_id>0)
BEGIN
--check si l'identite id est déjà adherent sur la formule pointée par le gestion_place_id
	
	declare @formuleAdhesionOfGp int

	declare @nbrThisAdhesionActive int

	select @formuleAdhesionOfGp = adh_offr.adhesion_catalog_id from gestion_place gp 
	inner join offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id
	inner join adhesion_catalog_offresliees adh_offr on adh_offr.offre_id = ogp.offre_id
	INNER JOIN Adhesion_Catalog_Propriete acpp ON acpp.Adhesion_Catalog_ID = adh_offr.Adhesion_Catalog_ID 
	AND acpp.Propriete_code ='TARIF_MAITRE' and acpp.Propriete_Valeur_Int1  = gp.type_tarif_id

	where gp.gestion_place_id = @pGpId 

	--select @formuleAdhesionOfGp

	select @nbrThisAdhesionActive = COUNT(*) from Adhesion_Adherent_Catalog link_adh_cat 
	INNER JOIN Adhesion_Adherent aa ON aa.Adhesion_Adherent_ID = link_adh_cat.Adhesion_Adherent_ID
	where link_adh_cat.Adhesion_Catalog_ID = @formuleAdhesionOfGp and link_adh_cat.Actif = 1 and link_adh_cat.Adhesion_DateFin > GETDATE()  
	and aa.Identite_id = @myidentite_id

	if (@nbrThisAdhesionActive = 0)
	begin
			-- /////// periode d'achat ???
			declare @catalogAchatPossible int
			select @catalogAchatPossible = COUNT(*) from Adhesion_Catalog_Propriete acpp WHERE acpp.Adhesion_Catalog_ID = @formuleAdhesionOfGp
			and Propriete_Code='PERIODE_ACHAT' and Propriete_Valeur_Date1 < GETDATE() and Propriete_Valeur_Date2 > GETDATE()
			if (@catalogAchatPossible>0)
			begin
				select 'OK:NEWADHESION' as ret
			end
			else
			begin
				select 'KO:ACHATIMPOSSIBLE' as ret
			end
	end
	else

	begin
		-- ////////////// j'ai déja cette adhesion, est ce que j'ai pris un des tarifs maitre sur cette manif ???

		select Propriete_Valeur_Int1 as tarif_maitre 
		into #myTarifsMaitres
		from Adhesion_Catalog_Propriete prop where prop.Propriete_Code ='TARIF_MAITRE' and prop.Adhesion_Catalog_ID = @formuleAdhesionOfGp

		--select * from #myTarifsMaitres
		declare @nTarifMaitresDejaPrisCetteSeance int 
		select @nTarifMaitresDejaPrisCetteSeance = COUNT(*) from Adhesion_Dossier_Entree adh_entree 
		inner join Adhesion_Adherent_Catalog aac on adh_entree.Adhesion_Adherent_Catalog_ID = aac.Adhesion_Adherent_Catalog_ID
		inner join Adhesion_Adherent aa on aa.Adhesion_Adherent_ID = aac.Adhesion_Adherent_ID
		inner join #myTarifsMaitres tarifM on tarifM.tarif_maitre = adh_entree.type_tarif_id
		where Manifestation_id = @manifId and Seance_id = @seanceId
		and aa.Identite_id = @myidentite_id

		if (@nTarifMaitresDejaPrisCetteSeance > 0)
		begin 
			select 'KO:ALREADYCONSUMED' as ret
		end
		else
		begin
			declare @checkHistoManif int /* c'est ok à la séance, doit on verifier l'histo à la manif (cf Adhesion_Catalog_Propriete where Propriete_Code='CTRLHISTO_TYPE')  ? */
			select @checkHistoManif = count(*)
			from Adhesion_Catalog_Propriete prop where prop.Adhesion_Catalog_ID = @formuleAdhesionOfGp and Propriete_Code='CTRLHISTO_TYPE' and Propriete_Valeur_Char1='MANIF'
			--select @checkHistoManif 
			if (@checkHistoManif>0)
			begin
					declare @nTarifMaitresDejaPrisCetteManif int 
					select @nTarifMaitresDejaPrisCetteManif = COUNT(*) from Adhesion_Dossier_Entree adh_entree 
					inner join Adhesion_Adherent_Catalog aac on adh_entree.Adhesion_Adherent_Catalog_ID = aac.Adhesion_Adherent_Catalog_ID
					inner join Adhesion_Adherent aa on aa.Adhesion_Adherent_ID = aac.Adhesion_Adherent_ID
					inner join #myTarifsMaitres tarifM on tarifM.tarif_maitre = adh_entree.type_tarif_id
					where Manifestation_id = @manifId 
					and aa.Identite_id = @myidentite_id
					if (@nTarifMaitresDejaPrisCetteManif > 0)
					begin 
						select 'KO:ALREADYCONSUMED' as ret
					end
					else
					begin
						select 'OK:NOTALREADYCONSUMED' as ret
					end
			end
			else
			begin
				select 'OK:NOTALREADYCONSUMED' as ret
			end
		end
		
		drop table #myTarifsMaitres

	end
	


END
ELSE
BEGIN
	select 'KO:CANTRETRIEVEIDENTITYID' as ret
	--THROW 51000, 'myidentite_id not found !', 1;  
END


/* cas :

- machin est déjà adherent de la formule/catalog et n'a pas encore pris cette séance (avec ce tarif) =====> ok, pas de rajout de carte à faire (remonter la carte active avec les infos)

- machin est déjà adherent de la formule/catalog a déjà pris cette séance (ou manif!) (avec ce tarif) =====> pas possible ! (remonter la carte active avec les infos)

- machin n'est pas adherent 
	- formule encore en vente ====> ok, il faudra ajouter la carte 
	
	- formule n'est plus en vente ===> pas possible !
errorcode = 0 

si deja adhérent
	offer == null
pas adherent
	offer = offer
	
	identite ???

déja adhérent et déja pris ce tarif (séance ou manif) selong prostock
	error code = 409 
	offer = null

pas adhérent mais est plus en vente (plus dans période d'achat)
	error code = 404
	offer = null


déja adhérent mais la carte est arrivée a échéance 



*/


