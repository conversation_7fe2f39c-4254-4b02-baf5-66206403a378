{"Version": 1, "WorkspaceRootPath": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\managers\\tst\\themissupporttoolsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.staging.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.local.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.local.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 190, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "ConfigIniKeysOfSection.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-07T09:53:17.332Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ConfigIniKeysOfSection.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "ViewState": "AgIAAG4AAAAAAAAAAADwvxwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T09:38:38.839Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Program.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Program.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Program.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Program.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Program.cs", "ViewState": "AgIAAFwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T11:06:51.146Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.local.json", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.local.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.local.json", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.local.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.local.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T11:06:45.743Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Development.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.Development.json", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Development.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.Development.json", "ViewState": "AgIAABIAAAAAAAAAAAAAACIAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-30T09:18:51.849Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.Staging.json", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Staging.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.Staging.json", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.Staging.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.Staging.json", "ViewState": "AgIAABIAAAAAAAAAAAAAACIAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-30T09:18:07.643Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ThemisSupportToolsManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAKQCAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:06:02.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ConfigIni.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "ViewState": "AgIAABAAAAAAAAAAAAAAACkCAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:05:31.736Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ConfigIni.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "ViewState": "AgIAAFcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-30T09:05:26.957Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "appsettings.json", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.json", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\appsettings.json", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\appsettings.json", "RelativeToolTip": "ThemisSupportTools.Web\\appsettings.json", "ViewState": "AgIAABIAAAAAAAAAAAAAAB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-30T08:44:47.115Z"}]}]}]}