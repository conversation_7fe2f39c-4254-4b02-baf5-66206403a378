{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Trace", "Microsoft.AspNetCore": "Trace"}}, "ConnectionStrings": {"WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true", "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true", "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true"}, "PathBase": "/tst/", "TypeRun": "TEST", "PathForSqlScript": "\\\\**************\\webservices\\dev\\libraries\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql", "TSTConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\{environment}\\{structureId}\\CONFIGSERVER\\config.ini.xml"}