﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="0429\**" />
    <Compile Remove="0994\**" />
    <Content Remove="0429\**" />
    <Content Remove="0994\**" />
    <EmbeddedResource Remove="0429\**" />
    <EmbeddedResource Remove="0994\**" />
    <None Remove="0429\**" />
    <None Remove="0994\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Pages\Class.cs" />
    <Compile Remove="Pages\Counter exo.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\css\intervenants.css" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.Typeahead" Version="4.7.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MudBlazor" Version="8.6.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj" />
  </ItemGroup>

</Project>
