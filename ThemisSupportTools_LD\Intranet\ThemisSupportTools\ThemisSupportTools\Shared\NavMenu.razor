﻿@using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
@inject NavigationManager _navigationManager
@inject IThemisSupportToolsManager _themisSupportToolsManager

<nav class="navbar bg-dark navbar-expand-lg " data-bs-theme="dark">
    <div class="container-fluid">
        <span class="navbar-brand">Thémis Support Tools</span>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarText" aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <AuthorizeView>
            <Authorized>
                <div class="collapse navbar-collapse" id="navbarText">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        @*<li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="#">Config Ini</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Features</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Pricing</a>
                        </li>*@
                    </ul>
                    <span class="navbar-text">

                        <div>Hello, @context.User.Identity.Name! </div>
                        <a class="form-label" href="account/logout">Se déconnecter </a>

                    </span>

                </div>
            </Authorized>
        </AuthorizeView>
    </div>
</nav>

@code {

    bool collapseNavMenu = true;
    private bool expandSubNavSettings;
    string baseMenuClass = "navbar-collapse d-sm-inline-flex flex-sm-row-reverse";
    string NavMenuCssClass => baseMenuClass + (collapseNavMenu ? " collapse" : "");


    void ToggleNavMenu()
    {
        if (!expandSubNavSettings)
        {
            collapseNavMenu = !collapseNavMenu;
        }
    }

    private void DisconnectUser()
    {
        _navigationManager.NavigateTo($"/account/logout");
    }

}


