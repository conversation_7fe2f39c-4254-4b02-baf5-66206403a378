﻿@page "/loginactivedirectory"
@page "/account/loginactivedirectory/{ErrorMessage}"
@using Core.Themis.Libraries.Utilities.Logging;
@using Microsoft.Extensions.Logging;

@inject NavigationManager NavManager
@inject IConfiguration _configuration
@attribute [AllowAnonymous]


<div class="container-fluid">
    <div class="row justify-content-md-center">

        <div class="col-6 login-form">

            @{
                string postUrl = _configuration["PathBase"] + "account/postloginactivedirectory";
            }

            <h3 class="login-title">TST Authentification</h3>
            <form method="post" action="@postUrl" class="form-control">
                <label>Username :</label>
                <input type="text" name="Username" class="form-control" placeholder="Your login" required />

                <label>Password :</label>
                <input type="password" name="Password" class="form-control password" placeholder="Your password" required/>

                <div class="d-grid gap-2 col-6 mx-auto mt-3 " style="padding : 2px">
                    <button type="submit" class="btn btn-primary">Log in</button>
                </div>
               
            </form>
  
            @if (ErrorMessage != null)
            {
                <div class="text-danger">@ErrorMessage</div>
            }

        </div>

    </div>
</div>



@code {

    [Parameter] public string ErrorMessage { get; set; } = string.Empty;
    public string PathBase { get; set; } = string.Empty;


     protected override void OnAfterRender(bool firstRender)
     {
         if (firstRender)
         {
             PathBase = _configuration["PathBase"]!;
         }
     }
    //Logger.LogWarning("Someone has clicked me!", User );
}
