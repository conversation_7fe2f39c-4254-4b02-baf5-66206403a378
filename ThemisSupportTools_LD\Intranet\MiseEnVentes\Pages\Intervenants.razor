﻿@page "/intervenants"
@inject ApplicationDbContext Db



<div class="header-actions-intervenant">
    <h1 class="Titre-intervenant">Gestion des intervenants</h1>
    <button class="btn btn-primary" @onclick="Modal_Add" id="createNewIntervenant">
        Ajouter un intervenant <i class="fas fa-plus-circle" aria-hidden="true"></i>
    </button>
</div>


<!-- Modal pour ajouter un intervenant -->

@if (showModalAdd_Intervenant)
{
    <div class="popup-overlay" @onclick="Modal_Add">
        <div class="popup-content" @onclick:stopPropagation>
            <h5>Ajout d'un Intervenant</h5>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fa-solid fa-circle-exclamation me-2"></i> @message
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer" @onclick="() => message = string.Empty"></button>
                </div>
            }


            <EditForm Model="@formAddModel" OnValidSubmit="AjouterIntervenant">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <label for="intervenantName" class="form-label">Nom de l'intervenant :</label>
                    <InputText id="intervenantName" class="form-control" @bind-Value="formAddModel.IntervenantName"  maxlength="30" />
                </div>

                <div class="mb-3">
                    <label for="intervenantEmail" class="form-label">Email :</label>
                    <InputText id="intervenantEmail" type="email" class="form-control" @bind-Value="formAddModel.IntervenantEmail" maxlength="30" />
                </div>

                <div class="d-flex justify-content-between">
                    <button class="btn btn-success" type="submit">Valider</button>
                    <button class="btn btn-danger" type="button" @onclick="Modal_Add">Annuler</button>
                </div>
            </EditForm>
        </div>
    </div>

}


<!-- Toast pour prévenir de l'action effectuer -->

@if (showToast)
{
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show toast-custom" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Succès</strong>
                <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
            </div>
            <div class="toast-body">
                L'intervenant a été ajoutée avec succès.
            </div>
        </div>
    </div>
}

<!-- Modal pour supprimer une mise en vente -->

@if (showDeleteModal)
{
    <div class="modal show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" @onclick="CancelDelete"></button>
                </div>
                <div class="modal-body">
                    <p style="font-weight: bold; color: black;">Es-tu sûr de vouloir supprimer cet intervenant ?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @onclick="CancelDelete">Annuler</button>
                    <button class="btn btn-danger" @onclick="DeleteIntervenant">Supprimer</button>
                </div>
            </div>
        </div>
    </div>
}

<!-- Toast pour prévenir de l'action effectuer -->

@if (showDeleteToast)
{
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show toast-custom-delete" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Suppression</strong>
                <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
            </div>
            <div class="toast-body">
                L'intervenant a été supprimée avec succès.
            </div>
        </div>
    </div>
}


<!-- Modal pour modifier un intervenant -->

@if (showModalEdit_Intervenant && IntervenantToEdit != null)
{
    <div class="popup-overlay" @onclick="CloseEditModal">
        <div class="popup-content" @onclick:stopPropagation>
            <h5>Modification de l'intervenant</h5>

            @if (!string.IsNullOrEmpty(message))
            {
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fa-solid fa-circle-exclamation me-2"></i> @message
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer" @onclick="() => message = string.Empty"></button>
                </div>
            }

            <EditForm Model="@editModel_inter" OnValidSubmit="EditIntervenant">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <label for="editIntervenantName" class="form-label">Nom de l'intervenant :</label>
                    <InputText id="editIntervenantName" class="form-control" @bind-Value="editModel_inter.IntervenantName" maxlength="30" />
                </div>

                <div class="mb-3">
                    <label for="editIntervenantEmail" class="form-label">Email :</label>
                    <InputText id="editIntervenantEmail" type="email" class="form-control" @bind-Value="editModel_inter.IntervenantEmail" maxlength="30" />
                </div>

                <div class="d-flex justify-content-between">
                    <button class="btn btn-primary" type="submit">Enregistrer</button>
                    <button class="btn btn-danger" type="button" @onclick="CloseEditModal">Annuler</button>
                </div>
            </EditForm>
        </div>
    </div>
}

<!-- Toast pour prévenir de l'action effectuer -->

@if (showEditToast)
{
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show toast-custom" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Succès</strong>
                <button type="button" class="btn-close" @onclick="HideToast" aria-label="Fermer"></button>
            </div>
            <div class="toast-body">
                L'intervenant a été modifier avec succès.
            </div>
        </div>
    </div>
}



<!-- Tableaux des Intervenants -->

@if (intervenants == null)
{
    <p>Chargement...</p>
}
else if (!intervenants.Any())
{
    <p>Aucun intervenant trouvé.</p>
}
else
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th style="border: 1px solid black;">Modifier</th>
                <th style="border: 1px solid black;">Nom</th>
                <th style="border: 1px solid black;">Email</th>
                <th style="border: 1px solid black;">Supprimer</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var i in intervenants)
            {
                <tr class="@(i.IdIntervenant == lastModifiedId ? "table-success" : "")">
                    <td style="border: 1px solid black;">
                        <button class="btn btn-sm btn-warning" @onclick="@(() => OpenEditModal_for_intervenant(i))">Modifier</button>
                    </td>
                    <td>@i.IntervenantName</td>
                    <td>@i.IntervenantEmail</td>
                    <td>
                        <button class="btn btn-sm btn-danger" @onclick="@(() => ConfirmDelete(i))">Supprimer</button>
                    </td>

                </tr>
            }
        </tbody>
    </table>
}
