{"ShowEmptyProjects": false, "CustomColumnOrderings": {"name": 0, "file-path": 1, "file-name": 2, "neutral-value": 3, "neutral-comment": 4, "type": 5, "de": 6, "en": 7, "fr": 8}, "ShowValidationErrors": true, "SelectedResourceGroupsByProject": {"D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj": [], "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj": ["D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.resx"]}, "VisibleColumnKeys": ["name", "neutral-value", "neutral-comment", "de", "en", "fr"]}