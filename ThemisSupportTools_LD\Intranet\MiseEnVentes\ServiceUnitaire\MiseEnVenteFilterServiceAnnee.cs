﻿using MiseEnVentes.Models;

namespace MiseEnVentes.ServiceUnitaire
{

        public class MiseEnVenteNewForTest
        {
        public int IdMiseEnVente { get; set; }

        public DateOnly? Date { get; set; }
        public TimeOnly? Hour { get; set; }

        public string? StructureId { get; set; }

        public bool? NotifMail { get; set; }

        public bool? NotifTicket { get; set; }

        public string? Commentaire { get; set; }

    }

    public class MiseEnVenteFilterServiceAnnee
    {
        public async Task<List<MiseEnVenteNewForTest>> FilterByYearAsync(IEnumerable<MiseEnVenteNewForTest> list, int selectedYear)
            {
                var today = DateOnly.FromDateTime(DateTime.Today);

                var query = list.AsQueryable();

                if (selectedYear == 0)
                {
                    query = query.Where(m => m.Date.HasValue && m.Date.Value >= today);
                }
                else
                {
                    query = query.Where(m => m.Date.HasValue && m.Date.Value.Year == selectedYear);
                }

                return await Task.FromResult(query.OrderBy(m => m.Date).ToList());
            }
        }

    }

