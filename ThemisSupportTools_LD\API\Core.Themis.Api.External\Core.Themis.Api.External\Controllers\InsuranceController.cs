﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Insurance.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.exposedObjects.Insurance;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.Orders.Details;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.Utilities.Crypto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using RestSharp;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.Json;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Insurance 
    /// </summary>
    [ApiController]
    [SwaggerTag("Internal")]
    [ApiExplorerSettings(GroupName = "internal")]
    public class InsuranceControlController : ControllerBase
    {

        private readonly IInsuranceManager _insuranceManager;
        private readonly IInsurancesContractsManager _insurancesContractsManager;
        private readonly IOrderManager _orderManager;
        private readonly ISessionManager _sessionManager;
        private readonly IMapper _mapper;

        private static readonly Libraries.Utilities.Logging.RodrigueNLogger Logger = new Libraries.Utilities.Logging.RodrigueNLogger();

        public InsuranceControlController(
            IMapper mapper,
            IInsuranceManager insuranceManager,
            IInsurancesContractsManager insurancesContractsManager,
            IOrderManager orderManager,
            ISessionManager sessionManager)
        {
            _mapper = mapper;
            _insuranceManager = insuranceManager;
            _insurancesContractsManager = insurancesContractsManager;
            _orderManager = orderManager;
            _sessionManager = sessionManager;
        }

        /// <summary>
        /// Activate cancellation insurance for a contract / barcode
        /// </summary>
        /// <param name = "partner">Partner Name</param>
        /// <param name = "numContrat">Contract ref</param>
        /// <param name = "items"> 
        ///  BODY POST   
        ///   {
        ///     "Items": 
        ///       [
        ///         {
        ///           "Id": 123
        ///         },
        ///         {
        ///           "Id": 567
        ///         }
        ///       ]
        ///     }       
        /// </param>
        /// <returns>Ok, KO if errors</returns>
        /// <remarks>Ok, KO if errors</remarks>
        [HttpPost]
        [Authorize(Roles = "Admin,Insurance")]
        [Route("api/insurance/activate/{partner}/{numContrat}")]
        public IActionResult activateInsurance(string partner, string numContrat, [FromBody] Items items)
        {

            int structureId = 0;
            int orderId;

            try
            {
                List<InsuranceContractDTO> listInsuranceContractDTO = _insurancesContractsManager.GetInsurancesContracts();
                InsuranceContractDTO? insuranceContractDTO = listInsuranceContractDTO.SingleOrDefault(ic => ic.ContractReference == numContrat);
                if (insuranceContractDTO == null)
                    return NotFound();

                structureId = insuranceContractDTO.StructureId;
                orderId = insuranceContractDTO.OrderId;
                #if !DEBUG
                    var accT = Request.Headers[HeaderNames.Authorization];
                    if (!TokenManager.PartnerHasRight(structureId, accT))
                        return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                #endif

                OrderInsurance orderInsurance = _insuranceManager.GetOrderSeats(structureId, orderId);

                ReturnItems returnItems = new ReturnItems();
                returnItems.listItems = new List<ItemWithMessage>();
                string msgErr;
                foreach (Item item in items.listItems)
                {
                    msgErr = string.Empty;
                    ItemWithMessage itemWithMessage = new ItemWithMessage();
                    itemWithMessage.Id = item.Id;
                    itemWithMessage.IsOk = true;

                    Logger.Debug(structureId, $"activateInsurance({numContrat}) item.Id={item.Id}");

                    SeatInsurance seatInsurance = orderInsurance.ListSeats.FirstOrDefault(ls => ls.Id == item.Id);
                    if (seatInsurance == null)
                    {
                        /*
                        msgErr = $"Error activating insurance for bar code {item.Id}.";
                        SeatInsurance foundSeat = orderInsurance.ListSeats.FirstOrDefault(ls => ls.InitialId == item.Id);
                        if (foundSeat != null)
                        {
                            if (String.IsNullOrEmpty(foundSeat.Id))
                                msgErr += $" The ticket {item.Id} was cancelled.";
                            else
                                msgErr += $" The ticket {item.Id} was duplicated. The new bar code is {foundSeat.Id}";
                        }
                        else
                        */
                            msgErr += " Item was not found.";
                    }
                    else if (!seatInsurance.IsInsuranceValid)
                    {
                        msgErr += $"Error activating insurance for item {item.Id}. {seatInsurance.ReasonInsuranceNotValid} ";
                    }
                    else if (!_insuranceManager.ActivateInsurance(partner, structureId, item.Id))
                    {
                        msgErr += $"Error activating insurance for item {item.Id}. Internal error.";
                    }

                    if (!String.IsNullOrEmpty(msgErr))
                    {
                        itemWithMessage.IsOk = false;
                        itemWithMessage.ErrorMessage = msgErr;
                        Logger.Error(structureId, $"ActivateInsurance(partner={partner},  structureId={structureId}, numContrat={numContrat}). {msgErr} ");
                    }

                    returnItems.listItems.Add(itemWithMessage);

                }

                return Ok(returnItems);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"ActivateInsurance(partner={partner}, structureId={structureId}, numContrat={numContrat}), ex.Message = {ex.Message}, ex.StackTrace = {ex.StackTrace}");
                return Problem("Error activating insurance .Message = " + ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }




        /// <summary>
        /// Get tickets linked to a contract
        /// </summary>
        /// <param name = "partner">Partner Name</param>
        /// <param name = "numContrat">Contract ref</param>
        /// <returns>list of tickets</returns>
        /// <remarks>Returns a list of tickets for this contract</remarks>
        [HttpGet]
        [Authorize(Roles = "Admin,Insurance")]
        [Route("api/insurance/{partner}/{numContrat}")]
        public IActionResult CheckInsurance(string partner, string numContrat)
        {

            int structureId;
            int orderId;

            List<InsuranceContractDTO> listInsuranceContractDTO = _insurancesContractsManager.GetInsurancesContracts();
            InsuranceContractDTO? insuranceContractDTO = listInsuranceContractDTO.SingleOrDefault(ic => ic.ContractReference == numContrat);
            if (insuranceContractDTO == null)
                return NotFound();
            else
            {

                structureId = insuranceContractDTO.StructureId;
                orderId = insuranceContractDTO.OrderId;
                #if !DEBUG
                    var accT = Request.Headers[HeaderNames.Authorization];
                    if (!TokenManager.PartnerHasRight(structureId, accT))
                        return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                #endif 
                Logger.Debug(structureId, $"CheckInsurance({numContrat}) orderId={orderId}");


                try
                {

                    OrderInsurance orderInsurance = _insuranceManager.GetOrderSeats(structureId, orderId);
                    return Ok(orderInsurance);

                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, $"CheckInsurance({numContrat} orderId ={orderId}, ex.Message = {ex.Message}, ex.StackTrace = {ex.StackTrace}");
                    return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
                }
            }

        }

        // Classes used by controllers for the body
        public class Items
        {
            public List<Item> listItems { get; set; }
        }

        public class Item
        {
            public string Id { get; set; }
        }

        // Classes used by controllers for the body
        public class ReturnItems
        {
            public List<ItemWithMessage> listItems { get; set; }
        }

        public class ItemWithMessage
        {
            public string Id { get; set; }
            public bool IsOk { get; set; }
            public string ErrorMessage { get; set; }
        }

    }

}