﻿
using Core.Themis.Libraries.BLL.adhesion_offres.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Partners.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.Utilities.API;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.CustomErrors;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;

namespace Core.Themis.API.Customers.Controllers
{
    //   [Authorize(Roles = "Admin, Viewer, User")]
    //[Route("api/[controller]")]
    [ApiController]
    public class IdentityController : ControllerBase
    {
        private readonly ILogger<IdentityController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContext;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
     
        private readonly IAdhesionOrdersManager _adhesionOrdersManager;
        private readonly IAdhesionCatalogManager _adhesionCatalogManager;
        private readonly IBasketManager _basketManager;
        private readonly IIdentiteManager _identiteManager;
        private readonly IUnidyManager _unidyManager;
        private readonly IMemoryCache _memoryCache;

        private static readonly RodrigueNLogger Logger = new();

        public IdentityController(
            ILogger<IdentityController> logger,
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IHttpContextAccessor httpContext,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IAdhesionCatalogManager adhesionCatalogManager,
            IAdhesionOrdersManager adhesionOrdersManager,
            IBasketManager basketManager,
            IIdentiteManager identiteManager,
            IUnidyManager unidyManager)
        {
            _logger = logger;
            _configuration = configuration;
            _memoryCache = memoryCache;
            _httpContext = httpContext;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _adhesionCatalogManager = adhesionCatalogManager;
            _adhesionOrdersManager = adhesionOrdersManager;
            _basketManager = basketManager;
            _identiteManager = identiteManager;
     
            _unidyManager = unidyManager;
        }

        /// <summary>
        /// Charge la liste des consommateurs pour une identité
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<IdentityDTO>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [HttpGet]
        [Route("api/{structureId}/identities/{identityId}/consumers")]
        public IActionResult LoadConsumers(int structureId, int identityId)
        {
            Logger.Trace(structureId, "LoadConsumer");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                var consumers = _identiteManager.LoadConsumers(structureId, identityId);
                return Ok(consumers);

            }
            catch (IdentityIdNotFoundException ex)
            {
                return Problem(ex.Message, null, (int)ex.ErrorCode);
            }
            catch (Exception ex)
            {
                return Problem(ex.Message, null);
            }
        }


        /// <summary>
        /// Charge les informations de l'identité via l'id du partenaire
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="partner"></param>
        /// <param name="externeId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/{structureId}/identities/{partner}/{externeId}")]
        public IActionResult LoadIdentity(int structureId, string partner, string externeId)
        {
            Logger.Trace(structureId, $"---- start LoadIdentity via partner {partner} {externeId}----");

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var indentiteInfos = _identiteManager.GetViaExterne(structureId, partner, externeId);

                return Ok(indentiteInfos);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "LoadIdentity:" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }


        /// <summary>
        /// Charge les informations de l'identité
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/{structureId}/identities/{identityId}")]
        public IActionResult LoadIdentity(int structureId, int identityId)
        {
            Logger.Trace(structureId, "---- start LoadIdentity ----");

            try
            {
                string cryptoKey = _configuration["CryptoKey"];

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                int ipostalTelEmail = 0;
                if (configIniXml.ContainsKey("VARIABLESEMAIL") && configIniXml["VARIABLESEMAIL"] != null && configIniXml["VARIABLESEMAIL"] != "")
                    ipostalTelEmail = int.Parse(configIniXml["VARIABLESEMAIL"].ToString());

                string todoLangIso = "en";

                var indentiteInfos = _identiteManager.Get(structureId, "", identityId, "0",  todoLangIso, cryptoKey);

                return Ok(indentiteInfos);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "LoadIdentity:" + ex.Message + " " + ex.StackTrace);
                throw;

            }
        }

        /// <summary>
        /// Créer un consommateur pour l'identite connectée
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>
        /// <param name="consumer">consommateur à ajouté</param>
        /// <returns></returns>
        [HttpPost]
        [Route("api/{structureId}/identities/{identityId}/consumers")]
        public IActionResult Consumer(int structureId, int identityId, IdentityDTO consumer)
        {
            Logger.Trace(structureId, "---- start create a Consumer ----");

            try
            {
                string cryptoKey = _configuration["CryptoKey"];

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                int indentityInfos = _identiteManager.AddConsumer(structureId, identityId, consumer);

                ConsumersAddedToList identityResult = new()
                {
                    Identities = new List<IdentityDTO>(),
                    StatusCode = HttpStatusCode.OK,
                    IndentityIdSelected = indentityInfos
                };

                //si le resultat de la requête est -1, le mail doit etre unique et il y a déja l'adresse email
                if (indentityInfos == -1)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = new List<IdentityDTO>(),
                        StatusCode = HttpStatusCode.Conflict,
                        IndentityIdSelected = indentityInfos
                    };
                    return Ok(identityResult);
                }
                var newConsumersList = LoadConsumers(structureId, identityId) as OkObjectResult;

                if (newConsumersList == null)
                {
                    Logger.Error(structureId, "LinkConsumer: la liste newConsumersList est null");
                    throw new ArgumentNullException();
                }

                List<IdentityDTO> consumers = (List<IdentityDTO>)newConsumersList.Value;
                if (consumers.Count > 0)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = consumers,
                        StatusCode = HttpStatusCode.OK,
                        IndentityIdSelected = indentityInfos
                    };
                }

                return Ok(identityResult);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "Consumer:" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }

        /// <summary>
        /// Recherche un consommateur grâce aux informations (ID + EMAIL ou ID + INITIALES ou EMAIL + INITIALES) et le rattache 
        /// Ou grâce au numéro d'adhérent
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityIdConnected"></param>
        /// <param name="identityIdToSeach"></param>
        /// <param name="initial">intiiales Nom + Prénom</param>
        /// <param name="email"></param>
        /// <param name="adherentId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/{structureId}/identities/{identityIdConnected}/consumers/searchAndLink")]
        public IActionResult SearchAndLinkConsumer(int structureId, int identityIdConnected, int identityIdToSeach, string initial, string email, string adherentId)
        {
            Logger.Trace(structureId, "---- start Search a Consumer ----");
            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                
                int indentityInfos = _identiteManager.SearchAndLinkConsumer(structureId, identityIdConnected, identityIdToSeach, initial, email, adherentId);

                ConsumersAddedToList identityResult = new()
                {
                    Identities = new List<IdentityDTO>(),
                    StatusCode = HttpStatusCode.OK,
                    IndentityIdSelected = indentityInfos
                };
                //Aucune identtie n'a été trouvée 
                if (indentityInfos == -1)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = new List<IdentityDTO>(),
                        StatusCode = HttpStatusCode.NotFound,
                        IndentityIdSelected = indentityInfos
                    };
                    return Ok(identityResult);
                }

                //Plusieurs identités
                if (indentityInfos == -2)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = new List<IdentityDTO>(),
                        StatusCode = HttpStatusCode.Conflict,
                        IndentityIdSelected = indentityInfos
                    };
                    return Ok(identityResult);
                }

                var newConsumersList = LoadConsumers(structureId, identityIdConnected) as OkObjectResult;

                if (newConsumersList == null)
                {
                    Logger.Error(structureId, "LinkConsumer: la liste newConsumersList est null");
                    throw new ArgumentNullException();
                }

                List<IdentityDTO> consumers = (List<IdentityDTO>)newConsumersList.Value;
                //List<IdentityEntity> consumers = (List<IdentityEntity>)LoadConsumers(structureId, identityIdConnected);
                if (consumers.Count > 0)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = consumers,
                        StatusCode = HttpStatusCode.OK,
                        IndentityIdSelected = indentityInfos
                    };
                }

                return Ok(identityResult);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "Search:" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }


        [HttpGet]
        [Route("api/{structureId}/identities/{identityIdConnected}/consumers/link")]

        public IActionResult LinkConsumer(int structureId, int identityIdConnected, int identityIdToLink)
        {
            Logger.Trace(structureId, "---- start LinkConsumer a Consumer ----");

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                int indentityInfos = _identiteManager.LinkConsumer(structureId, identityIdConnected, identityIdToLink);

                ConsumersAddedToList identityResult = new ConsumersAddedToList()
                {
                    Identities = new List<IdentityDTO>(),
                    StatusCode = HttpStatusCode.OK,
                    IndentityIdSelected = indentityInfos
                };

                var newConsumersList = LoadConsumers(structureId, identityIdConnected) as OkObjectResult;

                if (newConsumersList == null)
                {
                    Logger.Error(structureId, "LinkConsumer: la liste newConsumersList est null");
                    throw new ArgumentNullException();
                }

                List<IdentityDTO> consumers = (List<IdentityDTO>)newConsumersList.Value;

                if (consumers.Count > 0)
                {
                    identityResult = new ConsumersAddedToList()
                    {
                        Identities = consumers,
                        StatusCode = HttpStatusCode.OK,
                        IndentityIdSelected = indentityInfos
                    };
                }

                return Ok(identityResult);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "LinkConsumer:" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }


        [HttpPost]
        //[Route("api/{structureId}/identity")]
        [Route("api/{structureId}/identity/{checkemailunicity}")]
        public IActionResult AddIdentity(int structureId, IdentityDTO identity, bool checkemailunicity = true)
        {
            Logger.Trace(structureId, "---- start create an identity ----");

            try
            {
             
                int newIdentity = _identiteManager.AddIdentity(structureId, identity, checkemailunicity);

                //si le resultat de la requête est -1, le mail doit etre unique et il y a déja l'adresse email
                if (newIdentity == -1)
                {
                    CustomResponse identityResultEchec = new()
                    {
                        HttpStatusCode = HttpStatusCode.Ambiguous,
                        Object = newIdentity
                    };
                    //return Problem(identityResult);
                    return NotFound(identityResultEchec);
                }

                if (identity.ComplementWeb != null)
                {
                    int cw = _identiteManager.AddComplementWebOnIdentite(structureId, newIdentity, identity.ComplementWeb);
                    if (cw <= 0)
                    {
                        CustomResponse identityResultEchec = new()
                        {
                            HttpStatusCode = HttpStatusCode.Ambiguous,
                            Object = newIdentity
                        };
                        return Problem("can't add complementWeb");
                    }
                }

                // info comps ********
                List<int> infocompsSuccess = new List<int>();
                if (identity.LstInfoComp != null && identity.LstInfoComp.Count > 0)
                {
                    foreach (InfoCompDTO ic in identity.LstInfoComp)
                    {
                        //insert infocomps 
                        var infoCompOnIdentity = _identiteManager.AddInfoCompOnIdentite(structureId, newIdentity, ic);
                        if (infoCompOnIdentity > 0)
                        {
                            infocompsSuccess.Add(infoCompOnIdentity);
                        }
                    }
                    if (infocompsSuccess.Count != identity.LstInfoComp.Count)
                    {
                        return Problem();
                    }
                }

                CustomResponse identityResultSuccess = new CustomResponse
                {
                    HttpStatusCode = HttpStatusCode.OK,
                    Object = newIdentity
                };

                return Ok(identityResultSuccess);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, "Consumer:" + ex.Message + " " + ex.StackTrace);
                throw;
            }
        }

        /// <summary>
        /// Liste des commandes adhésions de l'identité 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>
        /// <returns></returns>
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/getadhesionsorders/{identityId}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<AdhesionDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]
        public IActionResult GetAdhesionsOrders(int structureId, int identityId)
        {
            Logger.Trace(structureId, $"GetAdhesionsOrders({structureId},{identityId})...");

            try
            {
                List<AdhesionDTO> adhesionOrdersOfIdentity = _adhesionOrdersManager.GetAdhesionOrdersOfIdentity(structureId, identityId);
                return Ok(adhesionOrdersOfIdentity);
            }
            catch (Exception ex)
            {
                Logger.Trace(structureId, $"Message = {ex.Message} GetAdhesionsToIdentity({structureId},{identityId}), stackTrace = {ex.StackTrace}");

                var pb = Problem(ex.Message, null);
                return pb;
            }


        }

        //est ce qu'on met ici

        [Route("api/{structureId}/getadhesions/{adhesionCatalogId}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<AdhesionCatalogDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]
        public IActionResult GetAdhesions(int structureId, int adhesionCatalogId)
        {
            Logger.Trace(structureId, $"GetAdhesionsCatalog({structureId},{adhesionCatalogId})...");

            try
            {
                List<AdhesionCatalogDTO> adhesionCatalogs = _adhesionCatalogManager.GetAdhesionCatalogs(structureId, new List<int>() { adhesionCatalogId });
                return Ok(adhesionCatalogs);

            }
            catch (Exception ex)
            {
                Logger.Trace(structureId, $"Message = {ex.Message} GetAdhesionsToIdentity({structureId},{adhesionCatalogId}), stackTrace = {ex.StackTrace}");
                return Problem(ex.Message, null);
            }
        }
    }
}
