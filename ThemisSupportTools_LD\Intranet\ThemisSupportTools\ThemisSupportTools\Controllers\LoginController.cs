﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.DirectoryServices;
using Core.Themis.Libraries.Utilities.Logging;
using ThemisSupportTools.Pages.Structures;

namespace ThemisSupportTools.Controllers
{

    public class LoginController : Controller
    {

        private static readonly RodrigueNLogger _rodrigueNLogger = new RodrigueNLogger();
        private readonly IConfiguration _configuration;
        public string PathBase { get; set; }

        public LoginController(IConfiguration configuration)
        {
            _configuration = configuration;
            PathBase = _configuration["PathBase"]!;
        }


        [HttpPost("account/postloginactivedirectory")]
        public async Task<IActionResult> Login(UserCredentials credentials)
        {
            _rodrigueNLogger.Info(0, $"get credentials for \"{credentials.Username}\"...");
            string path = "LDAP://ROD.local/dc=rod,dc=local";

            try
            {
                using (DirectoryEntry entry = new DirectoryEntry(path, credentials.Username, credentials.Password))
                {
                    using (DirectorySearcher searcher = new DirectorySearcher(entry))
                    {
                        searcher.Filter = "(samaccountname=" + credentials.Username + ")";
                        var result = searcher.FindOne();
                        if (result != null)
                        {
                            string role = "";
                            ResultPropertyCollection fields = result.Properties;
                            foreach (String ldapField in fields.PropertyNames)
                            {
                                foreach (Object myCollection in fields[ldapField])
                                {
                                    if (ldapField == "employeetype")
                                        role = myCollection.ToString().ToLower();
                                }
                            }
                            var claims = new[]
                            {
                                new Claim(ClaimTypes.Name, credentials.Username),
                                new Claim(ClaimTypes.Role, role)
                            };

                            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                            var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

                            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrincipal);

                            _rodrigueNLogger.Info(0, $"\"{credentials.Username}\" is inna place");

                            string urlredirect =  _configuration["PathBase"]! + "structureslist";

                            _rodrigueNLogger.Debug(0, $"urlredirect {urlredirect}");

                            //return Redirect(urlredirect);
                            return Redirect(urlredirect);
                        }
                        else
                        {
                            _rodrigueNLogger.Error(0, $"get credentials for  \"{credentials.Username}\" : searcher.FindOne() is null");
                            return Redirect($"loginactivedirectory/Invalid credentials");
                        }
                            
                        //return LocalRedirect($" {PathBase} loginactivedirectory/Invalid credentials");
                    }
                }
            }
            catch (Exception e)
            {
                _rodrigueNLogger.Warn(0, $"get credentials for  \"{credentials.Username}\" : {e.Message}");
                return Redirect($"loginactivedirectory/Login ou mot de passe incorrect");
                //return LocalRedirect($" {PathBase} loginactivedirectory/Login ou mot de passe incorrect");
            }
        }

        [HttpGet("account/logout")]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            PathBase = _configuration["PathBase"]!;
            return Redirect(PathBase);
        }

        public class UserCredentials
        {
            [Required]
            public required string Username { get; set; }

            [Required]
            public required string Password { get; set; }
        }


    }
}

