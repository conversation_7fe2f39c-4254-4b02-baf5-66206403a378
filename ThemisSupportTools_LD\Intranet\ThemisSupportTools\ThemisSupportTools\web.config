﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.webServer>
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
			<aspNetCore processPath="dotnet" arguments=".\ThemisSupportTools.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" >
				<environmentVariables>
					<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />
				</environmentVariables>
			</aspNetCore>
		</system.webServer>
	</location>
</configuration>
<!--ProjectGuid: 15e4d1cf-3e3e-46f8-b190-b2016d695f89--> 