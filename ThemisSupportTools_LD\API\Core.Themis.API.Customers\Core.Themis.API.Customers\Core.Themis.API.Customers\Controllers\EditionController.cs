﻿
using Core.Themis.Libraries.BLL.AccessControl.Interfaces;
using Core.Themis.Libraries.BLL.Helpers.Widgets;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.ExportEditionModel;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Core.Themis.API.Customers.Controllers
{
    //[Authorize(Roles = "Admin, Viewer, User")]
    public class EditionController : ControllerBase
    {

        private readonly IConfiguration _configuration;
        private readonly IPrintHomeManager _printHomeManager;
        private readonly IAccessControlManager _accessControleManager;
 
        private readonly ITranslateManager _translateManager;
        private readonly ISeatManager _seatManager;

        private static readonly RodrigueNLogger Logger = new();

        public EditionController(
            IConfiguration configuration,
         
            IPrintHomeManager printHomeManager,
            ITranslateManager translateManager,
            IAccessControlManager accessControleManager,
            ISeatManager seatManager)
        {
            _configuration = configuration;
            _printHomeManager = printHomeManager;
            _translateManager = translateManager;
            _accessControleManager = accessControleManager;
            _seatManager = seatManager;
        }

        /// <summary>
        /// print@home coupe file (panier dans web tracing)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/webThemis/printHome/{langCode}/{orderId}")]
        public IActionResult PrintHomeWebThemis(int structureId, string langCode, int orderId)
        {
            try
            {
                Logger.Debug(structureId, $"PrintHomeWebThemis({structureId},{langCode},{orderId})...");
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string fileName = _printHomeManager.DoPdfWebThemis(structureId, langCode, orderId, (List<int>?)null);

                Logger.Debug(structureId, $"PrintHomeWebThemis({structureId},{langCode},{orderId}) ok : {fileName}");
                var stream = new FileStream(fileName, FileMode.Open);

                using MemoryStream ms = new();
                stream.CopyTo(ms);
                byte[] pdf = ms.ToArray();

                return Ok(pdf);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"PrintHomeWebThemis({structureId},{langCode},{orderId}) :{ex.Message}");
                return Problem(ex.Message);
            }
        }

        [HttpPost]
        [Route("api/DownloadPdfbyMaquetteIdForTest/{structureId}/{langCode}/{maquetteId}")]
        public IActionResult DownloadPdfbyMaquetteIdForTest(int structureId,  string langCode, int maquetteId, [FromBody] Dictionary<string, string> dictionnary)
        {
            try
            {
                Logger.Debug(structureId, $"DownloadPdfbyMaquetteIdForTest({structureId},{langCode},{maquetteId})...");
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string pdf = _printHomeManager.GenerateMaquettePdfForTest(structureId, langCode, maquetteId, dictionnary ?? new());

                return Ok(pdf);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"DownloadPdfbyMaquetteIdForTest({structureId},{langCode},{maquetteId}) :{ex.Message}");
                return Problem(ex.Message);
            }
        }


        /// <summary>
        /// print@home coupe file (maquette coupe file)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="operateurid"></param>
        /// <param name="orderId"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/skipqueue/printHome/{langCode}/{operateurid}/{orderId}")]
        public IActionResult PrintHomeCoupeFile(int structureId, string langCode, int operateurid, int orderId)
        {
            try
            {
                Logger.Debug(structureId, $"PrintHomeCoupeFile({structureId},{langCode},{operateurid},{orderId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);


                string fileName = _printHomeManager.DoPdfCoupeFile(structureId, langCode, orderId, null);
                Logger.Debug(structureId, $"PrintHomeCoupeFile({structureId},{langCode},{operateurid},{orderId}) ok : {fileName}");

                return Ok(fileName);


            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"PrintHomeCoupeFile({structureId},{langCode},{operateurid},{orderId}) :{ex.Message}");
                return Problem(ex.Message);
            }
        }

        /// <summary>
        /// Check bar code (ok = not controlled and good eventId and good sessionId)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="barCode"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BarCodeDTO))]
        [Route("api/{structureId}/BarCodeCheck/{barCode}/{eventId}")]
        public IActionResult BarCodeCheck(int structureId, string barCode, int eventId)
        {
            try
            {
                Logger.Trace(structureId, $"BarCodeCheck ({structureId}, {barCode}, {eventId}...");
                var accT = Request.Headers[HeaderNames.Authorization];

                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                BarCodeDTO? brC = _accessControleManager.GetBarCodeByBarCode(structureId, barCode);

                if (brC == null)
                    return Problem($"barcode {barCode} not found", null, StatusCodes.Status404NotFound);

                if (brC.Controlled)
                    return Problem($"already controlled ({brC.ControlledDate})", null, StatusCodes.Status406NotAcceptable);

                if (brC.TypeOperation != "E" && brC.TypeOperation != "D")
                    return Problem($"TypeOperation {brC.TypeOperation} not acceptable", null, StatusCodes.Status406NotAcceptable);

                if (brC.EventId != eventId)
                    return Problem($"session mismatch", null, StatusCodes.Status406NotAcceptable);

                return Ok(brC);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }

        /// <summary>
        /// Get info of a bar code
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="barCode"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BarCodeDTO))]
        [Route("api/{structureId}/BarCode/{barCode}")]
        public IActionResult GetInfoBarCode(int structureId, string barCode)
        {
            try
            {
                Logger.Trace(structureId, $"GetInfoBarCode ({structureId}, {barCode}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                BarCodeDTO? brC = _accessControleManager.GetBarCodeByBarCode(structureId, barCode);

                return Ok(brC);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");

                return Problem(ex.Message);
            }
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BarCodeDTO))]
        [Route("api/{structureId}/BarCode/{barCode}")]
        public IActionResult SetBarCode(int structureId, string barCode, string newBarCode)
        {
            try
            {
                Logger.Trace(structureId, $"SetBarCode ({structureId}, {barCode}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                BarCodeDTO? brC = _accessControleManager.GetBarCodeByBarCode(structureId, barCode);

                if (brC == null)
                    return Problem("barcode doesn't exist");

                string partnerId = $"{TokenManager.getPartnerIdFromToken(accT)}";

                bool isUpdated = _accessControleManager.SetBarCode(newBarCode, barCode, brC.EventId, structureId, partnerId);

                if (!isUpdated)
                    return Problem($"can't update barcode {newBarCode}");

                return Ok(_accessControleManager.GetBarCodeByBarCode(structureId, barCode));
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }

        [HttpGet]
        [Route("api/{structureId}/DownloadPassbook/{identiteId}/{eventId}/{sessionId}/{orderId}/{seatId}/{backgroundColor}/{primaryColor}/{langCode}")]
        [Route("api/{structureId}/DownloadPassbook/{identiteId}/{eventId}/{sessionId}/{orderId}/{seatId}/{backgroundColor}/{primaryColor}/{langCode}/{sectionsToHide}")]
        public IActionResult DownloadPassbook(int structureId, int identiteId, int eventId, int sessionId, int orderId, int seatId, string backgroundColor, string primaryColor, string langCode, string sectionsToHide)
        {
            try
            {
                Logger.Trace(structureId, $"DownloadPassbook {structureId}, {identiteId}, {eventId}, {orderId}, {seatId}, {langCode}, {backgroundColor},{primaryColor}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var ticketInfos = _seatManager.GetSeatInfoForMTicket(structureId, eventId, seatId);

                if (ticketInfos == null)
                    return Problem($"Ticket informations doesn't exist");

                Dictionary<string, string> trad = _translateManager.GetDicoByLangCode(structureId, langCode, true);

                if (!string.IsNullOrEmpty(backgroundColor))
                    ticketInfos.SetBackgroundColor(backgroundColor);

                if (!string.IsNullOrEmpty(primaryColor))
                    ticketInfos.SetPrimaryColor(primaryColor);

                ticketInfos.TarifTitleTextTrad = trad["Widget_MTicket_LblTarif"];
                ticketInfos.PlacementTitleTextTrad = trad["Widget_MTicket_LblPlacement"];
                ticketInfos.FreePlacementLabelTextTrad = trad["Widget_MTicket_ValuePlacementLibre"];
                ticketInfos.ZoneTitleTextTrad = trad["Widget_MTicket_LblZone"];
                ticketInfos.FloorTitleTextTrad = trad["Widget_MTicket_LblFloor"];
                ticketInfos.SectionTitleTextTrad = trad["Widget_MTicket_LblSection"];

                var generatedPass = _printHomeManager.GenerateAppleTicket(structureId, ticketInfos);

                return Ok(generatedPass);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");
                return Problem(ex.Message, null);
            }
        }

        [HttpGet]
        [Route("Edition/GenerateMultiTicketsUrlByIdOrder")]
        public IActionResult GenerateMultiTicketsUrlByIdOrder(int structureId, string langCode, WalletType walletType, int orderId)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            var mtickets = _seatManager.GetAllSeatsInfoForMTicket(structureId, orderId);

            mtickets.ForEach(t =>
                SetMTicketTradAndParam(structureId, langCode, ref t)
            );

            switch (walletType)
            {
                case WalletType.Google:
                    var generatedGoogleUrl = _printHomeManager.GenerateMultiGoogleTicketsLink(structureId, mtickets);
                    return Ok(generatedGoogleUrl);
                case WalletType.Samsung:
                    var generatedSamsungUrl = _printHomeManager.GenerateMultiSamsungTicketsLink(structureId, mtickets);
                    return Ok(generatedSamsungUrl);
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Edition/GenerateMultiAppleTicketFileByOrderId")]
        public IActionResult GenerateMultiAppleTicketFileByOrderId(int structureId, string langCode, int orderId)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            var mtickets = _seatManager.GetAllSeatsInfoForMTicket(structureId, orderId);

            if (!mtickets.Any())
                return Problem($"Ticket informations doesn't exist");

            mtickets.ForEach(t =>
                SetMTicketTradAndParam(structureId, langCode, ref t)
            );

            byte[] generatedApplePass = _printHomeManager.GenerateMultiAppleTickets(structureId, mtickets);

            if (generatedApplePass == null)
                return Problem("Ticket can't be generated");

            return File(generatedApplePass, "application/vnd.apple.pkpasses", $"event-tickets-passbook-{orderId}.pkpasses.zip");
        }

        private static int[] ConvertSectionToHide(string sectionsToHide)
        {
            if (string.IsNullOrWhiteSpace(sectionsToHide))
                return Array.Empty<int>();

            return sectionsToHide.Split(',')
                                 .Select(elt => int.Parse(elt))
                                 .ToArray();
        }

        private void SetMTicketTradAndParam(int structureId, string langCode, ref MTicketModel model)
        {
            dynamic settingsMerged = WidgetUtilitiesHelper.SettingsMerged(structureId, 0, 0, "", langCode, "physicalPathOfSettingsCustomerJSON");
            string sectionsToHide = settingsMerged.mticket.sectionsToHide;
            string bgColor = settingsMerged.mticket.backgroundColor;
            string primaryColor = settingsMerged.mticket.primaryColor;

            Dictionary<string, string> trad = _translateManager.GetDicoByLangCode(structureId, langCode, true);

            model.TarifTitleTextTrad = trad["Widget_MTicket_LblTarif"];
            model.PlacementTitleTextTrad = trad["Widget_MTicket_LblPlacement"];
            model.FreePlacementLabelTextTrad = trad["Widget_MTicket_ValuePlacementLibre"];
            model.ZoneTitleTextTrad = trad["Widget_MTicket_LblZone"];
            model.FloorTitleTextTrad = trad["Widget_MTicket_LblFloor"];
            model.SectionTitleTextTrad = trad["Widget_MTicket_LblSection"];

            if (!string.IsNullOrWhiteSpace(bgColor))
                model.SetBackgroundColor(bgColor);

            if (!string.IsNullOrWhiteSpace(primaryColor))
                model.SetPrimaryColor(primaryColor);

            if (!string.IsNullOrWhiteSpace(sectionsToHide))
                model.SetHideSection(ConvertSectionToHide(sectionsToHide));
        }
    }
}
