﻿namespace MiseEnVentes;
using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;




public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Intervenant> Intervenants { get; set; }
    public virtual DbSet<MiseEnVenteNew> MisesEnVenteNew { get; set; }
    public virtual DbSet<MiseEnVenteIntervenant> MiseEnVenteIntervenants { get; set; }
    public virtual DbSet<Plateforme> Plateformes { get; set; }
    public virtual DbSet<MiseEnVentePlateforme> MiseEnVentePlateformes { get; set; }






    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<MiseEnVenteNew>().ToTable("MiseEnVenteNew");

        modelBuilder.Entity<Intervenant>().ToTable("Intervenant");
        modelBuilder.Entity<Plateforme>().ToTable("Plateforme");

        
        modelBuilder.Entity<MiseEnVenteIntervenant>().ToTable("MiseEnVenteIntervenant")
            .HasKey(mvi => mvi.IdMiseEnVenteIntervenant);
        modelBuilder.Entity<MiseEnVentePlateforme>().ToTable("MiseEnVentePlateforme")
           .HasKey(mvi =>  mvi.IdMiseEnVentePlateforme);

        modelBuilder.Entity<MiseEnVenteIntervenant>()
            .HasOne(mvi => mvi.Intervenant)
            .WithMany(i => i.MiseEnVenteIntervenants)
            .HasForeignKey(mvi => mvi.IdIntervenant);

        modelBuilder.Entity<MiseEnVenteIntervenant>()
            .HasOne(mvi => mvi.MiseEnVente)
            .WithMany(m => m.MiseEnVenteIntervenants)
            .HasForeignKey(mvi => mvi.IdMiseEnVente);

        
        modelBuilder.Entity<MiseEnVentePlateforme>()
            .HasOne(mvp => mvp.MiseEnVente)
            .WithMany(m => m.MiseEnVentePlateformes)
            .HasForeignKey(mvp => mvp.IdMiseEnVente);

        modelBuilder.Entity<MiseEnVentePlateforme>()
            .HasOne(mvp => mvp.Plateforme)
            .WithMany(p => p.MiseEnVentePlateformes)
            .HasForeignKey(mvp => mvp.IdPlateforme);


    }
}
