﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Data.SqlClient;

namespace Core.Themis.API.Catalog.Helpers
{
    public class ApiUtilities : IApiUtilities
    {
        private static readonly RodrigueNLogger Logger = new();

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;

        public ApiUtilities(
            IConfiguration configuration,
            IMemoryCache memoryCache)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
        }

        ///// <summary>
        ///// obtenir le partenaire et ses infos et roles à partir du nom, avec gestion d'un cache 
        ///// </summary>
        ///// <param name="partnerName">code partenaire</param>
        ///// <returns></returns>
        //public PartnerDTO GetPartnerIdAndRoles(string partnerName)
        //{
        //    PartnerDTO partner = new();
        //    string keyForPartnerInfo = $"GetPartnerIdAndRoles.name.{partnerName}";

        //    if (!_memoryCache.TryGetValue(keyForPartnerInfo, out partner))// Look for cache key.
        //    {
        //        SqlConnection sqlConnAdmin = GetAdminSqlConnetion();
        //        string pathScriptSqlCommons = _configuration["PathScriptSqlCommons"];

        //        partner = PartnerManager.GetPartnerIdAndRoles(partnerName, sqlConnAdmin, pathScriptSqlCommons, "apiOffer GetPartnerIdAndRoles");

        //        SetPartnerInfoInMemoryCache(keyForPartnerInfo, partner);
        //    }

        //    return partner;
        //}

        ///// <summary>
        ///// obtenir le partenaire et ses infos et roles à partir de l'id, avec gestion d'un cache 
        ///// </summary>
        ///// <param name="partnerId">partenaire id</param>
        ///// <returns></returns>
        //public PartnerDTO GetPartnerIdAndRoles(int partnerId)
        //{
        //    PartnerDTO partner = new();
        //    string getPartnerAndRolesCacheName = $"GetPartnerIdAndRoles.id.{partnerId}";
        //    if (!_memoryCache.TryGetValue(getPartnerAndRolesCacheName, out partner))// Look for cache key.
        //    {
        //        string pathScriptSqlCommons = _configuration["PathScriptSqlCommons"];
        //        SqlConnection sqlConnAdmin = GetAdminSqlConnetion();

        //        partner = PartnerManager.GetPartner(partnerId, sqlConnAdmin, pathScriptSqlCommons, "apiOffer");

        //        SetPartnerInfoInMemoryCache(getPartnerAndRolesCacheName, partner);
        //    }

        //    return partner;
        //}

        ///// <summary>
        ///// get connexion Open à partir du cache eventuel
        ///// </summary>
        ///// <param name="structureId"></param>
        ///// <param name="typeRun"></param>
        ///// <returns></returns>
        //public SqlConnection GetConnexionOpen(int structureId, string typeRun)
        //{
        //    try
        //    {
        //        string sqlConnOpenCacheName = $"SqlConnection[{structureId}.{typeRun}]";
        //        SqlConnection sqlConnOpen = new();
        //        string connStringOpen = "";

        //        if (!_memoryCache.TryGetValue(sqlConnOpenCacheName, out connStringOpen))// Look for cache key.
        //        {
        //            var wsAdminConnectionString = _configuration.GetConnectionString("WsAdminDB");
        //            sqlConnOpen = _openSqlConnection.ConnectToOpen(structureId, typeRun, wsAdminConnectionString, "api catalog apiutilities");

        //            if (sqlConnOpen == null)
        //            {
        //                Logger.Error(structureId, $"can't connect to db open for structure {structureId}");
        //                throw new Exception($"can't connect to db open for structure {structureId}");
        //            }
        //            else
        //            {
        //                var cacheEntryOptions = new MemoryCacheEntryOptions()
        //                   //Priority on removing when reaching size limit (memory pressure)
        //                   .SetPriority(CacheItemPriority.Normal)
        //                   // Keep in cache for this time, reset time if accessed.                           
        //                   // Remove from cache after this time, regardless of sliding expiration
        //                   .SetAbsoluteExpiration(TimeSpan.FromSeconds(120));
        //                _memoryCache.Set(sqlConnOpenCacheName, sqlConnOpen.ConnectionString, cacheEntryOptions);
        //            }
        //        }
        //        else
        //        {
        //            sqlConnOpen = new SqlConnection(connStringOpen);
        //        }
        //        return sqlConnOpen;
        //    }
        //    catch (Exception ex)
        //    {
        //        Logger.Error(structureId, $"GetConnexionOpen {ex.Message} \n {ex.StackTrace}");
        //        throw;
        //    }


        //}

        ///// <summary>
        ///// Get connexion WebLibraryDB
        ///// </summary>

        ///// <returns></returns>
        //public SqlConnection GetConnexionWebLibrary()
        //{
        //    try
        //    {
        //        var globalWebLibraryConnectionString = _configuration.GetConnectionString("WebLibraryDB");

        //        SqlConnection sqlConnWT = _webLibrary.ConnectToWebLibrary(globalWebLibraryConnectionString);

        //        return sqlConnWT;
        //    }
        //    catch
        //    {
        //        throw;
        //    }
        //}

        //private void SetPartnerInfoInMemoryCache(string partnerAndRolesCacheName, PartnerDTO partnerEntity)
        //{
        //    MemoryCacheEntryOptions cacheEntryOptions = new()
        //    {
        //        Priority = CacheItemPriority.High,
        //        AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(60),
        //    };

        //    _memoryCache.Set(partnerAndRolesCacheName, partnerEntity, cacheEntryOptions);
        //}

        //private SqlConnection GetAdminSqlConnetion()
        //{
        //    string wsAdminConnectionString = _configuration.GetConnectionString("WsAdminDB");
        //    return _wsAdminSqlConnection.ConnectToWsAdmin(wsAdminConnectionString);
        //}

    }
}
