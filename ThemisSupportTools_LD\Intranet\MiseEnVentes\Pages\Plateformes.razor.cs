﻿using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;

namespace MiseEnVentes.Pages
{
    public partial class Plateformes : ComponentBase
    {

        protected List<Plateforme>? plateformes = null;
        protected AddFormModelPlateforme formAddModel = new();

        protected string message = string.Empty;

        protected bool showModalAdd_Plateforme = false;

        protected bool showDeleteModal = false;
        protected Plateforme? PlateformeToDelete;

        protected Plateforme? PlateformeToEdit;
        protected bool showModalEdit_Plateforme = false;
        protected EditFormModel_For_Plateforme editModel_Plateforme = new();
        protected bool showEditModal = false;

        protected bool showToast = false;
        protected bool showEditToast = false;
        protected bool showDeleteToast = false;

        protected int? lastModifiedId = null;
        protected override async Task OnInitializedAsync()
        {
            plateformes = await Db.Plateformes
                .OrderBy(i => i.PlateformeName)
                .ToListAsync();
        }

        // Pour afficher le modal d'ajout d'une Plateforme
        protected void Modal_Add()
        {
            showModalAdd_Plateforme = !showModalAdd_Plateforme;
            if (showModalAdd_Plateforme == false)
                formAddModel = new(); // Reset si on ferme
        }

        // Methode pour ajouter une Plateforme
        protected async Task AddPlateforme()
        {
            bool existe = plateformes!.Any(i => i.PlateformeName == formAddModel.PlateformeName);

            if (existe)
            {
                message = "Cette Plateforme existe déjà.";
                return;
            }

            var nouvellePlateforme = new Plateforme
            {
                PlateformeName = formAddModel.PlateformeName
            };

            Db.Plateformes.Add(nouvellePlateforme);
            await Db.SaveChangesAsync();
            lastModifiedId = nouvellePlateforme?.IdPlateforme;

            plateformes?.Add(nouvellePlateforme);
            plateformes = plateformes?
                .OrderBy(i => i.PlateformeName)
                .ToList();

            showModalAdd_Plateforme = false;
            formAddModel = new();
            await ShowToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();
        }


        // Methode pour supprimer une Plateforme
        protected async Task DeletePlateforme()
        {
            if (PlateformeToDelete is not null && plateformes != null)
            {
                    Db.Plateformes.Remove(PlateformeToDelete);
                    await Db.SaveChangesAsync();
                    plateformes.Remove(PlateformeToDelete);

                    PlateformeToDelete = null;
                    showDeleteModal = false;
                    await ShowDeleteToast();
            }
        }

        protected void ConfirmDelete(Plateforme plateforme)
        {
            PlateformeToDelete = plateforme;
            showDeleteModal = true;
        }

        protected void CancelDelete()
        {
            showDeleteModal = false;
            PlateformeToDelete = null;
        }


        // Ouvre le modal pour modifier une Plateforme
        protected void OpenEditModal_for_Plateforme (Plateforme plateforme)
        {
            PlateformeToEdit = plateforme;

            editModel_Plateforme = new EditFormModel_For_Plateforme
            {
                PlateformeName = plateforme.PlateformeName,  
            };

            showModalEdit_Plateforme = true;
        }

        protected void CloseEditModal()
        {
            showModalEdit_Plateforme = false;
            PlateformeToEdit = null;
        }

        // Methode pour modifier une Plateforme
        protected async Task EditPlateforme()

        {
            if (PlateformeToEdit is not null)
            {
                PlateformeToEdit.PlateformeName = editModel_Plateforme.PlateformeName;

                await Db.SaveChangesAsync();
                lastModifiedId = PlateformeToEdit?.IdPlateforme;
            }

            showModalEdit_Plateforme = false;
            PlateformeToEdit = null;
            await ShowEditToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();

        }

        // Toast pour prévenir de l'action effectuer
        protected async Task ShowToast()
        {
            showToast = true;
            StateHasChanged(); // Affiche immédiatement

            await Task.Delay(5000);
            showToast = false;
            StateHasChanged(); // Cache le toast après 5s
        }

        protected void HideToast()
        {
            showToast = false;
            showEditToast = false;
        }

        protected async Task ShowEditToast()
        {
            showEditToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showEditToast = false;
            StateHasChanged();
        }

        protected async Task ShowDeleteToast()
        {
            showDeleteToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showDeleteToast = false;
            StateHasChanged();
        }

    }
}
