{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },

  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true"

  },

  "PathForSqlScript": "..\\..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "WsAdminConnectionCache": 300,
  "TypeRun": "TEST",

  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",

  "Cache": {
    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 2,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 2

  }
}
