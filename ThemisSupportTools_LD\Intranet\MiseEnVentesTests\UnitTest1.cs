﻿using Xunit;

//Test pour vérifier qu'au moins un intervenant a été séléctionné
public class IntervenantManagerTests
{
    [Theory]
    [InlineData(1, true, true)]   // id = 1, isChecked = true => doit être présent
    [InlineData(2, false, false)] // id = 2, isChecked = false => ne doit pas être présent
    [InlineData(3, true, true)]
    [InlineData(3, false, false)] // même id que précédemment, mais décoché
    public void OnIntervenantCheckedChanged_Should_Add_Id_When_Checked_True(int id, bool isChecked, bool shouldContain)
    {
        // Arrange
        var manager = new IntervenantManager();

        // Act
        manager.OnIntervenantCheckedChanged(id, isChecked);

        // Assert
        if (shouldContain)
            Assert.Contains(id, manager.Intervenants);
        else
            Assert.DoesNotContain(id, manager.Intervenants);
    }

   
}
