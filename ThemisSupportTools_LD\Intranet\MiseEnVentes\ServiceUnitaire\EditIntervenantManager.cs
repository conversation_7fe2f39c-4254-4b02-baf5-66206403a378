﻿namespace MiseEnVentes.ServiceUnitaire
{

    public class EditModelTest
    {
        public List<int> Intervenants { get; set; } = new();
    }
    public class EditIntervenantManager
    {
        public EditModelTest Model { get; set; } = new();

        public void OnEditIntervenantCheckedChanged(int intervenantId, object checkedValue)
        {
            var isChecked = (bool)checkedValue;

            if (isChecked)
            {
                if (!Model.Intervenants.Contains(intervenantId))
                    Model.Intervenants.Add(intervenantId);
            }
            else
            {
                if (Model.Intervenants.Contains(intervenantId))
                    Model.Intervenants.Remove(intervenantId);
            }
        }
    }
}
