using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Core.Themis.API.External.Helpers
{
    public class ActivetrailObjects
    {
        public class ContactItem
        {

            /*{
            "firstname": "<PERSON><PERSON>",
            "lastname": "<PERSON><PERSON><PERSON>",
             "email": "<EMAIL>",
            "sms": "+33660661055"
           }*/

            public string FirstName { get; set; }

            public string LastName { get; set; }
            public string Email { get; set; }

            public string SMS { get; set; }

           // [JsonPropertyName("camp_id")]
           // public int CampId { get; set; }

           
        }
}
}
