﻿using System.ComponentModel.DataAnnotations;

namespace MiseEnVentes.Models
{
    public class AddFormModeIntervenant
    {
        public int IdIntervenant { get; set; }

        [Required(ErrorMessage = "Le nom est requis.")]
        public string IntervenantName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "L'email est requis.")]
        [EmailAddress(ErrorMessage = "Format email invalide.")]
        public string IntervenantEmail { get; set; } = string.Empty;
    }
}
