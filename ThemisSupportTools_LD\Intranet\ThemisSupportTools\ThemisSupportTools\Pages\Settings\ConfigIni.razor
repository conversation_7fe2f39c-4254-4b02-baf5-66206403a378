﻿@page "/configini/{structureId:int}/{environment}"

@using System.Xml;
@using BlazorBootstrap;
@using Newtonsoft.Json;
@using System.Text.Json;
@using System.Linq.Expressions;
@using System.Text.Json.Serialization;
@using Core.Themis.Libraries.BLL.Extentions;
@using Core.Themis.Libraries.Utilities.Extensions;
@using Core.Themis.Libraries.Utilities.Logging;

@attribute [Authorize]
@inject IJSRuntime _js
@implements IDisposable
@inject IConfiguration _configuration
@inject RodrigueNLogger _rodrigueLogger
@inject NavigationManager NavigationManager
@inject NavigationManager _navigationManager
@inject IThemisSupportToolsManager _themisSupportToolsManager



<ConfirmDialog @ref="dialog" />

<Toasts class="p-3" Messages="toastMessages" Delay="6000" Placement="@toastsPlacement" />

<div class="position-relative">
    <div class="position-absolute top-0 end-0">
        <Alert Color="AlertColor.Danger" Class="position-fixed">
            <Icon Name="IconName.ExclamationTriangleFill" class="me-2"></Icon><strong>Temps restant : @TimeLeft</strong>
        </Alert>
    </div>
</div>


<button type="button" class="btn btn-lg btn-danger btn-floating rounded-circle" @onclick="ScrollToTop" id="btn-back-to-top">
    <Icon Name="IconName.ArrowBarUp"></Icon>
</button>
<div>
    <span>@StructureId</span>
    <span>@Environment</span>
</div>
<div class="">
    <div class="row">
        <div class="p-2 bg-light rounded-3">
            <form>
                <div class="form-control" disabled="@IsDisabledSearchSelect">
                    <label>Filtre Groupe de clés :</label>
                    <InputSelect class="form-select" area-label="Choix Filtre" id="sectionFilter" Value="@SelectedSection"
                                 ValueChanged="@((string value) => OnSectionChanged(value))" ValueExpression="@(()=>SelectedSection)">
                        <option value="">Choisir une Section...</option>
                        @foreach (var sec in SectionNameForFilter)
                        {
                            <option value="@sec" disabled="@IsDisabledSearchSelect">@sec</option>
                        }
                    </InputSelect>

                    <label>Recherche par clés :</label>
                    <input class="form-control" type="search" placeholder="Renseignez une clé..." aria-label="Search"
                           disabled="@IsDisabledSearchSelect" @bind="SearchTerm" @onkeyup="@OnKeyUp" @bind:event="oninput">

                    <div class="form-check">
                        <input type="checkbox" disabled="@IsDisabledMust" checked="@IsMandatoryFieldsChecked" @oninput="CheckboxChanged" class="form-check-input" id="checkIsMandatoryField">
                        <label class="form-check-label" for="checkIsMandatoryField">Afficher les champs obligatoires</label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" disabled="@IsDisabledModifiedField" checked="@IsModifiedFieldsChecked" @oninput="CheckboxModifiedFieldChanged" class="form-check-input" id="checkIsModifiedField">
                        <label class="form-check-label" for="checkIsModifiedField">Afficher les champs modifiés</label>
                    </div>

                    <div class="form-control-sm">
                        <button class="btn btn-secondary" @onclick="ShowRestoreConfirmationAsync" @onclick:preventDefault> Restaurer le dernier fichier</button>
                        <button class="btn btn-danger" @onclick="DeleteTmpFileOnChangeState" @onclick:preventDefault> Changer de structure</button>
                        @* <button class="btn btn-primary" @onclick="ShowConfigIniRecap" @onclick:preventDefault>Récapitulatif avant enregistrement</button>
                        <input type="checkbox" checked @oninput="CheckboxModifiedFieldChanged" class="btn-check" id="checkIsModifiedField" autocomplete="off">
                        <label class="btn btn-outline-secondary" for="checkIsModifiedField">Afficher les champs modifiés</label> *@
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <PopUp @ref="popup"></PopUp>

        <div class="col">
            @if (IsLoading)
            {
                <div class="spinner-border text-info" role="status">
                    <span class="visually-hidden">Chargement en cours...</span>
                </div>
            }
            else
            {
                <EditForm Model="ConfigIniSections" OnValidSubmit="@OnValidSubmit" class="@FormClass">
                    <button class="form-control btn btn-success fixed-bottom" type="submit">Sauvegarder</button>

                    <div class="accordion border border-1 sections-sortable " id="settings_sections_customer_wrapper">
                        @{
                            int cpt = 0;
                            foreach (var configIni in ConfigIniFiltered) //ConfigIniSections.Where(s => s.IsVisible && s.SectionFields.Any(f => f.IsVisible)))
                            {
                                string accordionCollapsed = "";
                                string accordionDivCollapse = "";

                                if (!configIni.ChildrenHasValue)
                                {
                                    accordionCollapsed = "collapsed";
                                    accordionDivCollapse = "collapse";
                                }

                                <div class="settings_section" data-id="@configIni.SectionName">
                                    <h2 class="accordion-header border border-2" id="heading_customer_@cpt">
                                        <button class="accordion-button @accordionCollapsed" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#collapse_customer_@cpt" aria-expanded="false" aria-controls="collapse_customer_@cpt">
                                            @configIni.SectionName
                                        </button>
                                    </h2>
                                    <div id="collapse_customer_@cpt" class="accordion-collapse @accordionDivCollapse" aria-labelledby="heading_customer_@cpt"
                                         data-bs-parent="#accordion_customer">

                                        <div class="accordion-body">
                                            <div class="row sections-items-sortable" data-id="@configIni.SectionName">

                                                @foreach (var field in configIni.SectionFields) //configIni.SectionFields.Where(f => f.IsVisible))
                                                {

                                                    string borderColor = "border-success is-valid";

                                                    if (string.IsNullOrWhiteSpace(field.FieldValue))
                                                        borderColor = "border-secondary";

                                                    if (field.IsMandatory && string.IsNullOrWhiteSpace(field.FieldValue))
                                                        borderColor = "border-danger is-invalid";

                                                    <div class="col-12 settings_section_item border-3 border-start p-2 @borderColor" data-parent="@configIni.SectionName">

                                                        <DynamicLabel Isrequired="@field.IsMandatory" Comment="@field.Comment" CssClass="@borderColor">@field.FieldName</DynamicLabel>

                                                        @if (field.Type.ToLower() == TSTFieldTypeEnum.Select.ToString().ToLower())
                                                        {
                                                            // <InputSelect ValueExpression="@(()=>field.FieldValue)" class="@($"form-control {borderColor}")" Value="@field.FieldValue"
                                                            //              ValueChanged="@((string value) => OnValueChanged(field, value))" @attributes="field.HtmlAttributes">
                                                            //     <DynamicSelect PlaceHolder="@KeyNotUsed" SelectItems="@field.SelectData" ></DynamicSelect>
                                                            // </InputSelect>

                                                            // <select class="@($"form-control {borderColor}")"  @bind="@field.FieldValue" @attributes="field.HtmlAttributes">
                                                            //     <DynamicSelect PlaceHolder="@KeyNotUsed" SelectItems="@field.SelectData" IsModified="field.IsModified"></DynamicSelect>
                                                            // </select>
                                                            <select class="@($"form-control {borderColor}")" @onchange="((e) => ChangeValue(e, configIni.SectionName, field.FieldName))" @attributes="field.HtmlAttributes">
                                                                <DynamicSelect PlaceHolder="@KeyNotUsed" SelectItems="@field.SelectData"></DynamicSelect>
                                                            </select>
                                                            <div class="invalid-feedback">@field.MandatoryMessage</div>
                                                        }
                                                        else
                                                        {
                                                            <input @onchange="((e) => ChangeValue(e, configIni.SectionName, field.FieldName))" value="@field.FieldValue" placeholder="@KeyNotUsed" class="form-control @borderColor" @attributes="field.HtmlAttributes" />
                                                            <div class="invalid-feedback ">@field.MandatoryMessage</div>
                                                        }
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                cpt++;
                            }
                        }
                    </div>
                </EditForm>
            }
        </div>
    </div>

</div>

@code {

    #region Parametres
    [CascadingParameter]
    private Task<AuthenticationState> authenticationState { get; set; }
    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object> Dict { get; set; } = new Dictionary<string, object>();
    [Parameter]
    public int StructureId { get; set; }
    [Parameter]
    public string Environment { get; set; } = string.Empty;
    [Parameter]
    public string Name { get; set; } = string.Empty;
    #endregion

    #region Propriétés
    public string PathBase { get; set; } = string.Empty;

    private string CurrentUser = string.Empty;
    private string ConfigIniUser = string.Empty;
    private string KeyNotUsed = "Clé non utilisée";
    private bool IsMandatoryFieldsChecked;
    private bool IsModifiedFieldsChecked;

    private List<ConfigIniSectionDTO> ConfigIniFiltered = new();
    private List<ConfigIniSectionDTO> ConfigIniSections = new();
    private List<ConfigIniSectionDTO> ConfigIniExceptions = new();

    private bool IsLoading = true;
    private string SearchTerm = string.Empty;
    private string FormClass = "needs-validation";
    private string SelectedSection = string.Empty;
    private List<string> SectionNameForFilter = new();
    private PopUp popup = new();
    private ConfirmDialog dialog = default!;
    TimeSpan TimeLeft = new TimeSpan(0, 30, 00);
    List<ToastMessage> toastMessages = new List<ToastMessage>();
    ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;
    protected bool IsDisabledModifiedField { get; set; }
    protected bool IsDisabledMust { get; set; }
    protected bool IsDisabledSearchSelect { get; set; }
    #endregion

    /// <summary>
    /// Initialisation de la page
    /// </summary>
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        // _rodrigueLogger.Info(0, "configinisettings.OnAfterRenderAsync...");

        if (firstRender)
        {
            PathBase = _configuration["PathBase"]!;
            bool hasAccessToPage = true;

            var user = (await authenticationState).User;
            CurrentUser = user.Identity.Name;
            ConfigIniUser = _themisSupportToolsManager.GetConfigIniUser(StructureId, Environment);

            _rodrigueLogger.Info(StructureId, $"LoadConfig.ini {StructureId}, {Environment}, {CurrentUser}...");

            //Check si l'utilisateur à un fichier temporaire et supprime ce fichier temporaire si la date de création est supérieur au temps du timer
            bool hasTempFile = _themisSupportToolsManager.HasTempFile(StructureId, Environment, ConfigIniUser);

            if (hasTempFile)
            {
                var isExpiredTempfile = _themisSupportToolsManager.IsTempFileOld(StructureId, Environment, ConfigIniUser, TimeLeft);
                if (isExpiredTempfile)
                {
                    //Suppression Fichier Temporaire et Chargement de la page
                    bool isDeleted = _themisSupportToolsManager.DeleteConfigIniTempFile(StructureId, Environment, ConfigIniUser);
                    if (isDeleted)
                        hasTempFile = false;
                }
                if (CurrentUser != ConfigIniUser)
                {
                    _rodrigueLogger.Warn(StructureId, $"LoadConfig.ini {StructureId}, {Environment}, {CurrentUser}: Fichier de {StructureId} en cours d'utilisation par {ConfigIniUser.ToUpper()}");

                    var options = new ConfirmDialogOptions
                        {
                            YesButtonText = "J'ai compris",
                            YesButtonColor = ButtonColor.Success,
                            NoButtonText = string.Empty,
                        };

                    await ShowConfirmationAsync(options, "Themis Support Tools", $"ConfigIni de {StructureId} ({Environment}) en cours d'utilisation par {ConfigIniUser.ToUpper()}, merci de changer de structure !");
                    _navigationManager.NavigateTo($"");
                    hasAccessToPage = false;
                }
            }

            if (hasAccessToPage)
            {
                //_rodrigueLogger.Info(StructureId, $"{CurrentUser}, {StructureId}, {Environment} hasAccessToPage ok");
                if (!hasTempFile)
                {
                    _themisSupportToolsManager.CreateTempFile(StructureId, Environment, CurrentUser);
                }

                ConfigIniSections = _themisSupportToolsManager.GetCustomerFusion(StructureId, Environment);
                //Récupération des exceptions ***** Nouveau ConfigIniSections sans exceptions
                ConfigIniExceptions = _themisSupportToolsManager.GetExceptionsModel(ConfigIniSections);
                //Nettoyage des exceptions
                ConfigIniSections = _themisSupportToolsManager.DeleteExceptionsExternes(ConfigIniSections);
                _themisSupportToolsManager.SetSelectDatas(ConfigIniSections);

                _themisSupportToolsManager.SetSelectValue(ConfigIniSections);
                SectionNameForFilter = _themisSupportToolsManager.GetConfigIniDefaultSections(StructureId, Environment);

                ConfigIniFiltered = ConfigIniSections;
                Timer();

                _rodrigueLogger.Info(StructureId, $"LoadConfig.ini {StructureId}, {Environment}, {CurrentUser} ok");
            }

            IsLoading = false;
            StateHasChanged();
        }
    }

    public void Dispose()
    {
        //Confirmation si l'utilisateur souhaite quitter la page
        ConfigIniUser = _themisSupportToolsManager.GetConfigIniUser(StructureId, Environment);
        bool hasTempFile = _themisSupportToolsManager.HasTempFile(StructureId, Environment, ConfigIniUser);
        if (hasTempFile)
        {
            _themisSupportToolsManager.DeleteConfigIniTempFile(StructureId, Environment, ConfigIniUser);
        }
    }


    void ChangeValue(ChangeEventArgs e, string sectionName, string fieldModified)
    {
        var section = ConfigIniSections.Single(s => s.SectionName.ToUpper() == sectionName.ToUpper());
        var field = section.SectionFields.Single(f => f.FieldName.ToUpper() == fieldModified.ToUpper());

        field.FieldValue = e.Value.ToString();
        field.IsModified = true;
    }


    #region Restauration & Changement de structure
    private async Task ShowRestoreConfirmationAsync()
    {
        var options = new ConfirmDialogOptions
            {
                YesButtonText = "RESTAURER",
                YesButtonColor = ButtonColor.Success,
                NoButtonText = "ANNULER",
                NoButtonColor = ButtonColor.Danger
            };

        if (await ShowConfirmationAsync(options, "Themis Support Tools", "Etes-vous sûr de vouloir restaurer le dernier fichier sauvegardé ?"))
        {
            ConfigIniSections = _themisSupportToolsManager.GetBackupFusion(StructureId, Environment, "__ConfigIniBackup");
            _rodrigueLogger.Info(StructureId, $"config.ini {StructureId}, {Environment}, {CurrentUser} fichier restauré");
        }
    }

    private async Task DeleteTmpFileOnChangeState()
    {
        var options = new ConfirmDialogOptions
            {
                YesButtonText = "CHANGER DE STRUCTURE",
                YesButtonColor = ButtonColor.Success,
                NoButtonText = "ANNULER",
                NoButtonColor = ButtonColor.Danger
            };

        if (await ShowConfirmationAsync(options, "Themis Support Tools", "Etes-vous sûr de vouloir changer de structure ?"))
        {
            _themisSupportToolsManager.DeleteConfigIniTempFile(StructureId, Environment, CurrentUser);
            _rodrigueLogger.Info(StructureId, $"config.ini {StructureId}, {Environment}, {CurrentUser} change structure");
            _navigationManager.NavigateTo($"");
        }
    }

    #endregion

    #region Prévisualisation du fichier

    private async Task ShowConfigIniRecap()
    {
        var options = new ConfirmDialogOptions
            {
                IsScrollable = true,
                Size = DialogSize.Large,
            };

        ConfigIniSections = _themisSupportToolsManager.CleanConfigIniModel(ConfigIniSections);
        var recap = _themisSupportToolsManager.CreateFinalCustomerXml(ConfigIniSections);

        string jsonConfigIni = JsonConvert.SerializeObject(recap, Newtonsoft.Json.Formatting.Indented);
        await ShowConfirmationAsync(options, "Themis Support Tools", jsonConfigIni);
    }

    #endregion

    #region Validation du formulaire
    /// <summary>
    /// Validation du formulaire
    /// </summary>
    private async Task OnValidSubmit()
    {
        //Check si le formulaire est valide
        if (CheckIsValidForm())
        {
            var options = new ConfirmDialogOptions
                {
                    YesButtonText = "SAUVEGARDER",
                    YesButtonColor = ButtonColor.Success,
                    NoButtonText = "ANNULER",
                    NoButtonColor = ButtonColor.Danger
                };

            //Affiche la popup de confirmation
            if (await ShowConfirmationAsync(options, "Themis Support Tools", "Etes-vous sûr de vouloir sauvegarder les modifications ?"))
            {
                ConfigIniSections = _themisSupportToolsManager.CleanConfigIniModel(ConfigIniSections);
                //Ajout des exceptions autres
                ConfigIniSections = _themisSupportToolsManager.AddCustomerExceptions(ConfigIniSections, ConfigIniExceptions);
                XDocument finalCustomerXml = _themisSupportToolsManager.CreateFinalCustomerXml(ConfigIniSections);
                _themisSupportToolsManager.CreateBackupSaveConfigIniXml(StructureId, Environment, finalCustomerXml);
                _themisSupportToolsManager.DeleteConfigIniTempFile(StructureId, Environment, CurrentUser);


                _rodrigueLogger.Info(StructureId, $"config.ini {StructureId}, {Environment}, {CurrentUser} save config.ini");
                //PopUp Validation avec succès et redirection page d'accueil
                popup.Show("Sauvegarde modifications avec succès, Well Done !", "Retour à l'accueil", "Thémis Support Tools");
            }
        }
        else
        {
            ToastMessage toastMessage = new()
                {
                    Type = ToastType.Danger,
                    AutoHide = true,
                    Title = "Validation du formulaire",
                    Message = "Il reste des champs obligatoires vides"
                };

            toastMessages.Add(toastMessage);
        }
    }


    /// <summary>
    /// Vérifie si le formulaire est valide (champs obligatoire)
    /// </summary>
    private bool CheckIsValidForm()
    {
        ConfigIniSections.ForEach(section =>
        {
            section.SectionFields.ForEach(field =>
            {
                field.IsValid = true;

                if (field.IsMandatory && string.IsNullOrWhiteSpace(field.FieldValue))
                    field.IsValid = false;
            });
        });
        FormClass = "was-validated";

        _js.InvokeVoidAsync("scrollToControl", ".form-control:invalid");
        return ConfigIniSections.All(s => s.SectionFields.All(f => f.IsValid));
    }

    #endregion

    #region Timer
    private async Task Timer()
    {
        while (TimeLeft > new TimeSpan())
        {
            await Task.Delay(1000);
            TimeLeft = TimeLeft.Subtract(new TimeSpan(0, 0, 1));
            StateHasChanged();
        }
        await AfterTime();
        StateHasChanged();
    }

    private async Task AfterTime()
    {
        var options = new ConfirmDialogOptions
            {
                YesButtonText = "J'ai compris",
                YesButtonColor = ButtonColor.Success,
                NoButtonText = string.Empty //cache le bouton No
            };
        _rodrigueLogger.Info(StructureId, $"config.ini {StructureId}, {Environment}, {CurrentUser} temps depassé");
        if (await ShowConfirmationAsync(options, "Themis Support Tools", "Le temps est écoulé. Nous allons vous rediriger vers la page d'accueil"))
        {
            _themisSupportToolsManager.DeleteConfigIniTempFile(StructureId, Environment, CurrentUser);
            _navigationManager.NavigateTo($"");
        }

    }
    #endregion

    #region Confirmations, Alert

    private async Task<bool> ShowConfirmationAsync(ConfirmDialogOptions dialogOptions, string title, string message)
    {
        var confirmation = await dialog.ShowAsync(

            title: title,
            message1: message,
            confirmDialogOptions: dialogOptions

            );

        if (confirmation)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    private void ShowInfoAlert()
    {
        popup.Show("Aucune modification sur ce formulaire !", "J'ai compris !", "Thémis Support Tools");
    }

    #endregion

    #region Filtre, Recherche

    private async Task OnValueChanged(ConfigIniSectionFieldDTO field, string value)
    {
        field.FieldValue = value;
        if (!string.IsNullOrWhiteSpace(field.Groupe))
        {
            _themisSupportToolsManager.SetSelectDatas(ConfigIniSections);
        }
    }


    /// <summary>
    /// Affiche les champs obligatoires
    /// </summary>
    private void CheckboxChanged(ChangeEventArgs e)
    {
        var value = Convert.ToBoolean(e.Value);
        if (value)
        {
            _themisSupportToolsManager.SetSelectDatas(ConfigIniFiltered);
            ConfigIniFiltered = ConfigIniSections.Where(s => s.SectionFields.Any(f => f.IsMandatory)).Select(s => new ConfigIniSectionDTO()
                {
                    ChildrenHasValue = s.ChildrenHasValue,
                    IsDisabled = s.IsDisabled,
                    IsVisible = s.IsVisible,
                    SectionEtat = s.SectionEtat,
                    SectionFields = s.SectionFields.Where(f => f.IsMandatory).ToList(),
                    SectionName = s.SectionName

                }).ToList();

            IsDisabledSearchSelect = true;
            IsDisabledModifiedField = true;
        }
        else
        {
            IsDisabledModifiedField = false;
            IsDisabledSearchSelect = false;
            ConfigIniFiltered = ConfigIniSections;
        }
    }

    /// <summary>
    /// Affiche les champs modifiés
    /// </summary>
    private void CheckboxModifiedFieldChanged(ChangeEventArgs e)
    {
        var value = Convert.ToBoolean(e.Value);
        if (value)
        {
            _themisSupportToolsManager.SetSelectDatas(ConfigIniFiltered);
            ConfigIniFiltered = ConfigIniSections.Where(s => s.SectionFields.Any(f => f.IsModified)).Select(s => new ConfigIniSectionDTO()
                {
                    ChildrenHasValue = s.ChildrenHasValue,
                    IsDisabled = s.IsDisabled,
                    IsVisible = s.IsVisible,
                    SectionEtat = s.SectionEtat,
                    SectionFields = s.SectionFields.Where(f => f.IsModified).ToList(),
                    SectionName = s.SectionName

                }).ToList();

            //Aucun champs modifiés
            NoFieldModified(ConfigIniFiltered, value);

        }
        else
        {
            ModifiedFieldCheckedFalse();
        }
    }

    public void ModifiedFieldCheckedFalse()
    {
        IsDisabledMust = false;
        IsDisabledSearchSelect = false;
        ConfigIniFiltered = ConfigIniSections;
    }

    public void ModifiedFieldCheckedTrue()
    {
        IsDisabledSearchSelect = true;
        IsDisabledMust = true;
    }

    public void NoFieldModified(List<ConfigIniSectionDTO> configIni, bool val)
    {
        if (configIni.Count == 0)
        {
            val = false;
            ConfigIniFiltered = ConfigIniSections;
            ModifiedFieldCheckedTrue();
            ShowInfoAlert();
        }
        else
        {
            ModifiedFieldCheckedTrue();
        }
    }

    /// <summary>
    /// Recherche par nom de sections (liste déroulante)
    /// </summary>
    void OnSectionChanged(string sectionName)
    {
        SelectedSection = sectionName;
        FilterConfigIni(new ConfigIniFilterModel()
            {
                SectionName = sectionName,
                KeywordValue = SearchTerm

            });
    }


    /// <summary>
    /// Recherche par clés (champs input text)
    /// </summary>
    void OnKeyUp(KeyboardEventArgs arg)
    {
        //ConfigIniSections = ConfigIniSectionsForSearch;
        FilterConfigIni(new ConfigIniFilterModel()
            {
                KeywordValue = SearchTerm,
                SectionName = SelectedSection
            });
    }

    private void FilterConfigIni(ConfigIniFilterModel filter)
    {
        _themisSupportToolsManager.SetSelectDatas(ConfigIniFiltered);
        ConfigIniFiltered = _themisSupportToolsManager.GetFilteredConfigIni(ConfigIniSections.Copy(), filter);
    }
    #endregion


    #region ScrollToTop & ScrollToDown
    private void ScrollToTop()
    {
        _js.InvokeVoidAsync("scrollToTop");
    }
    #endregion
}
