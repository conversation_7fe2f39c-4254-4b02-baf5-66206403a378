﻿DECLARE @ICEXIST INT;
SELECT @ICEXIST=count(*) FROM identite_infos_comp WHERE identite_id=[IDENTITEID] AND info_comp_id=[INFOCOMPID]

IF (@ICEXIST=0)
	INSERT INTO identite_infos_comp (identite_id,info_comp_id,valeur1,valeur2,valeur3,valeur4,supprimer,datecreation,datemodification) VALUES (
		[IDENTITEID],[INFOCOMPID],'','','','','N',getdate(),getdate());

 ELSE 
	UPDATE identite_infos_comp SET supprimer='N' , datemodification=getdate() 
	WHERE identite_id=[IDENTITEID] AND info_comp_id=[INFOCOMPID];