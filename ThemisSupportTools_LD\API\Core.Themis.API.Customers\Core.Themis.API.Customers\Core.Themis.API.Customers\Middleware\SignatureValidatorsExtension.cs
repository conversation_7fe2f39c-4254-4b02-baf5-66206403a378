﻿
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace Core.Themis.API.Customers.Middleware
{
    public static class SignatureValidatorsExtension
    {
        public static IApplicationBuilder ApplySignatureValidation(this IApplicationBuilder app)
        {
            app.UseMiddleware<SignatureValidatorsMiddleware>();
            return app;
        }
    }


    public class SignatureValidatorsMiddleware
    {
        private static readonly NLog.Logger Logger = NLog.LogManager.GetCurrentClassLogger();


        private readonly RequestDelegate _next;



        public SignatureValidatorsMiddleware(
            RequestDelegate next)
        {
            _next = next;
         
        }
        public async Task Invoke(HttpContext context, IPartnerManager partnerManager)
        {
            if (!context.Request.Headers.Keys.Contains("Signature"))
            {
                Logger.Error("Aucune signature dans le header");
                context.Response.StatusCode = 400; //Bad Request                
                await context.Response.WriteAsync("Signature is missing");
                return;
            }
            else
            {
                string mySignatureRecue = context.Request.Headers["Signature"].ToString();

                string displUrl = context.Request.GetDisplayUrl();

                int myId = int.Parse(context.User.Identity.Name);

                PartnerDTO part = partnerManager.GetPartnerInfosById( myId);

                string mySecretKey = part.SecretKey;
                string toHash = context.Request.Method + " " + displUrl;


                string signatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, mySecretKey);
                if (signatureCalculee != mySignatureRecue)
                {
                    Logger.Error($"toHash {toHash} ");
                    Logger.Error($"La signature calculée n'est pas la même que la signature reçue : \n calculée : {signatureCalculee} - reçue : {mySignatureRecue}");
                    //https://localhost:44329/api/CrossSelling?structureId=994

                    context.Response.StatusCode = 401; //UnAuthorized
                    await context.Response.WriteAsync("Invalid User Key");
                    return;
                }

            }
            await _next.Invoke(context);
        }
    }

}
