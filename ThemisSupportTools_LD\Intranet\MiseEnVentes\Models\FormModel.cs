﻿using System.ComponentModel.DataAnnotations;
using static MiseEnVentes.Pages.IndexBase;

namespace MiseEnVentes.Models
{
    public class FormModel
    {
        [Required(ErrorMessage = "La date est requis.")]
        public DateTime Date { get; set; } = DateTime.Today;
        [Required(ErrorMessage = "L'heure est requis.")]
        public string? Heure { get; set; }
        public List<int> Plateformes { get; set; } = new List<int>();
        [Required(ErrorMessage = "Une structure doit être selectionnée.")]
        public string? Structure { get; set; }
        //[AtLeastOneRequired(ErrorMessage = "")]
        public List<int> Intervenants { get; set; } = new List<int>();
        public bool Mail { get; set; }
        public bool Ticket { get; set; }
        [StringLength(300, ErrorMessage = "Le commentaire ne doit pas dépasser 300 caractères.")]
        public string? Commentaire { get; set; } = null;
        public ImportanceNiveau? Importance { get; set; } = ImportanceNiveau.Normal;

    }

    public enum ImportanceNiveau
    {
        Normal = 0,
        Moyen = 1,
        Eleve = 2
    }
    public enum ImportanceNiveauCachee
    {
        Normal = 0,
        Moyen = 1,
        Eleve = 2
    }

}
