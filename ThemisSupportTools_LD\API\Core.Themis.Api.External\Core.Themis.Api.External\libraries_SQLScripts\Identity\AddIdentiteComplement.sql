/*
declare @pIdentiteId int 
declare @pLangueId int = 0
declare @pCommentaire varchar(max) = ''
declare @pSex varchar(30) =''
*/

INSERT INTO Identite_complement (identite_id, num_icone1, num_icone2, num_icone3, num_icone4,
	commentaire, TVA_intracommunautaire, langue_id, civilite, photo, Nationality) 
VALUES(@pIdentiteId, 0, 0, 0, 0, @pCommentaire, '', @pLangueId, @pSex, NULL, 0)
where 0 = (select  COUNT(*)  from Identite_complement where identite_id=@pIdentiteId)





