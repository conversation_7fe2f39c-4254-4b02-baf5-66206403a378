﻿@page "/editpartenaire"
@page "/editpartenaire/{PartnerId:int}"
@*@rendermode InteractiveServer*@

@using Core.Themis.Libraries.Razor.Common.Components.Select
@using Core.Themis.Libraries.Razor.Common.Components
@inject IConfiguration _configuration
@inject IWsAdminStructuresManager WsAdminStructuresManager
@inject IPartnerManager PartnersManager

@inject NavigationManager NavigationManager

<EditForm Model="PartnerDTO" OnValidSubmit="@SavePartnerAsync">
    <DataAnnotationsValidator /> Paramètres
    <div class="form-group">
        <!--Hidden field for partner Id-->
        <input type="hidden" @bind="PartnerDTO.PartnerId" />
        <label>NOM:</label>
        <div>
            <InputText cssClass="form-control" @bind-Value="@PartnerDTO.PartnerName" />
            <ValidationMessage For="@(() => PartnerDTO.PartnerName)" />
        </div>
    </div>
    <div class="form-group" mb-3>
        <label>Secret Key:</label>
        <div>
            <InputText cssClass="form-control" @bind-Value="@PartnerDTO.SecretKey" />
            <span><button type="button" class="btn btn-secondary" @onclick:preventDefault="false" @onclick="GenerateSecretKey">Nouvelle secret key</button></span>
            @*<span><InputCheckbox  cssClass="form-check-input" @bind-Value="@this.IsChecked" @bind-Value:after="CheckboxChanged"></InputCheckbox></span>*@
            <ValidationMessage For="@(() => PartnerDTO.SecretKey)" />
        </div>
    </div>

    <div class="form-group" mb-3>
        <span> <label>Structures: <SearchBox OnSearch="@OnSearchStructuresAsync"></SearchBox></label></span>
        <span>
            <MultipleSelect SelectItemsLookup="SelectLookupStructures"></MultipleSelect>
          
        </span>
    </div>

    <div class="form-group">
        <span> <label>Roles: <SearchBox OnSearch="@OnSearchRolesAsync"></SearchBox></label></span>
        <span>
            <MultipleSelect SelectItemsLookup="SelectLookupRoles"></MultipleSelect>
        </span>
    </div>

    <div class="text-end">
        <button type="button" class="btn btn-secondary" @onclick="Cancel">Annuler</button>
        <button type="submit" class="btn btn-primary" @onclick="SavePartnerAsync">Valider</button>
    </div>
</EditForm>

@code {

    [Parameter]
    public int PartnerId { get; set; }

    public PartnerDTO PartnerDTO { get; set; } = new();
    private List<Core.Themis.Libraries.DTO.PartnerDTO> _partenaires = new();

    /* [Parameter]*/
    public List<StructureDTO> StructuresDTO { get; set; } = new List<StructureDTO>();
    /* [Parameter]*/
    public List<PartnerRoleDTO> PartnerRolesDTO { get; set; } = new List<PartnerRoleDTO>();
    //[Parameter] public EventCallback SavePartner { get; set; }
    public List<Core.Themis.Libraries.DTO.WSAdmin.WsAdminStructureDTO> WsAdminStructuresDTO = new List<Core.Themis.Libraries.DTO.WSAdmin.WsAdminStructureDTO>();

    private List<SelectLookup> SelectLookupStructures = new();
    private List<SelectLookup> SelectLookupRoles = new();


    public string PathBase { get; set; } = string.Empty;


    //protected override async Task OnInitializedAsync()
    //{

    //    SelectLookupStructures = await WsAdminStructuresManager.GetSelectLookupStructuresAsync().ConfigureAwait(false);
    //    SelectLookupRoles = await WsAdminStructuresManager.GetSelectLookupRolesAsync().ConfigureAwait(false);
    //}
    protected override Task OnInitializedAsync()
    {
        PathBase = _configuration["PathBase"]!;
        return base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {

        if (PartnerId > 0)
        {
            PartnerDTO = await PartnersManager.GetPartnerInfosByIdAsync(PartnerId);

            SelectLookupStructures = await PartnersManager.GetSelectLookupStructuresByPartnerAsync(PartnerId) ?? new();

            SelectLookupRoles = await PartnersManager.GetSelectLookupRolesByPartnerAsync(PartnerId) ?? new();
        }


        //this.SelectLookupRoles = this.PartnerDTO.LstRolesOfPartner

        //    .Select ( lrs => new SelectLookup()

        //    {
        //        Value = lrs.PartnerRoleId,
        //        Libelle = lrs.PartnerRoleCode
        //    }).ToList();

        //this.SelectLookupStructures = this.PartnerDTO.LstStructuresLinked
        //    .Select(lsl => new SelectLookup()
        //    {
        //        Value = lsl.StructureId,
        //        Libelle = lsl.StructureName
        //    }).ToList();
    }
    private void GenerateSecretKey()
    {
        PartnerDTO.SecretKey = GenerateRandomPassword();
    }
    private async Task SavePartnerAsync()
    {

        if (!string.IsNullOrWhiteSpace(PartnerDTO.PartnerName) && !string.IsNullOrWhiteSpace(PartnerDTO.SecretKey))
        {


            PartnerDTO.LstStructuresLinked = SelectLookupStructures.Where(slr => slr.IsSelected).Select(x => new Core.Themis.Libraries.DTO.WSAdmin.WsAdminStructureDTO()
            {
                StructureId = x.Value.ToString("0000"),
                Name = x.Libelle


            }).ToList();
            PartnerDTO.LstRolesOfPartner = SelectLookupRoles.Where(slr => slr.IsSelected).Select(x => new PartnerRoleDTO()
            {
                PartnerRoleId = x.Value,
                PartnerRoleCode = x.Libelle
            }).ToList();



            await PartnersManager.UpdatePartnerWithDependanciesAsync(PartnerDTO);


            NavigationManager.NavigateTo("partners");


            //        PartenairesManager.UpdatePartenaireRoles(partnerId, SelectLookupRoles.Select(slr => slr.Value).ToList());
            //      PartenairesManager.UpdatePartenaireRoles(partnerId, SelectLookupStructures.Select(sls => sls.Value).ToList());


        }
    }

    //[Parameter]
    //public string SecretKey { get; set; } = string.Empty;

    //public bool OperatorChoiceValue { get; set; } = false;


    //private string SetSecretKeyValue(bool b )
    //{
    //    return b?
    //}

    //private void     CheckboxChanged(ChangeEventArgs e)
    //{
    //    //// Generate random password  as a secret key
    //    //int length = 12;
    //    //string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
    //    //var random = new Random();
    //    //string password = new string(Enumerable.Repeat(chars, length)
    //    //    .Select(s => s[random.Next(s.Length)]).ToArray());

    //    //return password;

    //    OperatorChoiceValue = Convert.ToBoolean(e.Value);


    //}
    private string GenerateRandomPassword()
    {
        // Generate random password  as a secret key
        int length = 12;
        string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
        var random = new Random();
        string password = new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());

        return password;
    }

    private async Task OnSearchStructuresAsync(string searchTerm)
    {

        var filteredStructures = await WsAdminStructuresManager.SearchSelectLookupStructuresAsync(searchTerm).ConfigureAwait(false);
        SelectLookupStructures = filteredStructures?.OrderBy(s => s.Value).ToList();
    }

    private async Task OnSearchRolesAsync(string searchTerm)
    {
        var filteredRoles = await WsAdminStructuresManager.SearchSelectLookupRolesAsync(searchTerm).ConfigureAwait(false);
        SelectLookupRoles = filteredRoles?.OrderBy(s => s.Value).ToList();
    }

    private void Cancel()
    {
        NavigationManager.NavigateTo("/partners");
    }

}