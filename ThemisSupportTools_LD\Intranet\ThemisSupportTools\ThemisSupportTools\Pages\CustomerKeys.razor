﻿@page "/customerKeys"

<h3>CustomerKeys</h3>

@using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
@using Core.Themis.Libraries.DTO.WSAdmin;
@inject NavigationManager NavigationManager

@using System.Xml;
@using System.Xml.Linq;
@using System;
@using Microsoft.XmlDiffPatch;


<div class= "form-control" style="padding:10px">
    <button class="" >Get Différence</button>
    <button class="" @onclick="GetDifferencesFusion">Get Diff Keys </button>
    @*   <p class="oi-action"><strong>TEST Missing default Keys : @TestMissingKeysNumber</strong></p>
    <p class="oi-action"><strong>PROD Missing default Keys : @ProdMissingKeysNumber</strong></p>*@
    <p class="oi-action"><strong>FUSION Missing default Keys : @DevMissingKeysNumber</strong></p>
</div>


<Grid TItem="WsAdminStructureDTO" Class="table table-hover table-bordered table-striped" DataProvider="CustomerDataProvider"  
        AllowFiltering="true" Responsive="true" AllowPaging="true"  PageSize="20" AllowSorting="true" AllowSelection="true"
        ItemsPerPageText="10" PageSizeSelectorItems="new int[] { 10, 20, 50 }" PaginationItemsTextFormat="">

    <GridColumn TItem="WsAdminStructureDTO" HeaderText="Id" PropertyName="StructureId" SortKeySelector="item => item.StructureId">
        @context.StructureId
    </GridColumn>

    <GridColumn TItem="WsAdminStructureDTO" HeaderText="Name" PropertyName="Name" SortKeySelector="item => item.Name">
        @context.Name
    </GridColumn>

@*  <GridColumn TItem="WsAdminStructureDTO" HeaderText="Status" PropertyName="IsDeleted" SortKeySelector="item => item.IsDeleted">
        @context.IsDeleted
    </GridColumn >*@

    <GridColumn TItem="WsAdminStructureDTO" HeaderText="Environment">
      <div class="d-grid gap-2 d-md-flex justify-content-center mt-2">
          <Button @onclick=@(() => SelectStructure(@context.StructureId, "DEV")) Color="ButtonColor.Secondary" Class="oi">DEV</Button>
          <Button @onclick=@(() => SelectStructure(@context.StructureId, "TEST")) Color="ButtonColor.Primary" Class="">TEST</Button>
          <Button @onclick=@(() => SelectStructure(@context.StructureId, "PROD")) Color="ButtonColor.Danger" Class="">PROD</Button>
      </div>
    </GridColumn>   

</Grid>


 
@code {

    public int defaultKeysNumber;
    public int customerKeysNumber;
    public int DevMissingKeysNumber;
    public int ProdMissingKeysNumber;
    public int TestMissingKeysNumber;
    public List<string> MissingConfigIniFile = new List<string>();

    private List<WsAdminStructureDTO> structures;

    [Inject]
    private IWsAdminStructuresManager _structuresManager { get; set; }

    public List<string> EnvironmentList = new List<string> { "DEV", "TEST", "PROD" };

    public List<string> StructuresList = new List<string>();


    protected override void OnInitialized()
    {

        GetStructuresList();

        // defaultKeysNumber = 0;
        // customerKeysNumber = 0;
        // DevMissingKeysNumber = 0;
        // TestMissingKeysNumber = 0;
        // ProdMissingKeysNumber = 0;


        // var defaultIni = XDocument.Load("D:\\TST_SCRIPT_CUSTOMER_KEYS\\DefaultConfig.ini.xml");
        // var customerIni = XDocument.Load("D:\\TST_SCRIPT_CUSTOMER_KEYS\\CustomerConfig.ini.xml");
        // string diffPath = $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\diff.ini-Copie.xml";



        //Each Envionnement
        // FusionEnvironmentKeys();

        //Each Customer
        // foreach (string env in EnvironmentList)
        // {
        //     PrintEachCustomerDifferences(defaultIni, customerIni, env);S
        // }

        //FUSION
        //GetDifferencesFusion();

        // CompareFiles(defaultIni, customerIni, diffPath);
        // GetConfigIniFusion(customerIni, defaultIni);
        //var customerIni = XDocument.Load("\\\\Srv-paiement64\\customerfiles\\TEST\\0434\\CONFIGSERVER\\config.ini.xml");

    }

    void SelectStructure(string structureId, string environement)
    {
        NavigationManager.NavigateTo($"/structuresettings/{structureId}/{environement}");
    }

    /// <summary>
    /// Compare ConfigIni Client et Default
    /// Récupère toutes les clés présentes et sections dans le client et absentes dans le default
    /// </summary>
    public void CompareFiles(XDocument fichierClient, XDocument fichierDefaut, string filePathName)
    {

        XDocument xmlDoc = new XDocument(new XDeclaration("1.0", "iso-8859-1", "yes"));
        XElement root = new XElement("configIni");
        XElement newSection = null;
        XAttribute newAttribute = null;

        customerKeysNumber = fichierClient.Descendants("Section").Elements().Count();
        defaultKeysNumber = fichierDefaut.Descendants("Section").Elements().Count();

        foreach (var item in fichierClient.Descendants("Section").Attributes("Name"))
        {
            var sectionName = item.Value;

            var clientElements = from elements in fichierClient.Descendants("configIni").Elements("Section")
                                 where (string)elements.Attribute("Name") == sectionName
                                 select elements;

            //default section
            var defautElements = from de in fichierDefaut.Descendants("configIni").Elements("Section")
                                 where (string?)de.Attribute("Name") == sectionName
                                 select de;
            //
            if (defautElements != null)
            {
                if (clientElements.Elements().Count() != defautElements.Elements().Count())
                {
                    newSection = new XElement("Section");
                    newAttribute = new XAttribute("Name", sectionName);
                    newSection.Add(newAttribute);

                    //récupérer et comparer les enfants
                    foreach (var customerKey in clientElements.Elements())
                    {
                        var keyExiste = defautElements.Elements().Where(a => a.Name.LocalName.ToUpper() == customerKey.Name.LocalName.ToUpper()).FirstOrDefault();

                        if (keyExiste == null)
                        {

                            ProdMissingKeysNumber++;
                            newSection.Add(customerKey);
                        }
                    } 
                    if (newSection.Elements().Count() != 0)
                    {
                        root.Add(newSection);
                    }

                }

            }
        }
        if (root.Elements().Count() != 0)
        {
            //EachStrucure EachEnvironment
            xmlDoc.Add(root);
            SaveDifferences(xmlDoc, filePathName);

            //FusionEnvironnement
            //xmlDoc.Save("D:\\diff.ini.xml");

        }


    }

    private List<WsAdminStructureDTO> GetStructuresList()
    {
        structures = _structuresManager.GetWsAdminStructures_getActives();
        return structures;
    }




    public bool IsDifferent(XDocument defaultFile, XDocument customerFile)
    {
        if (defaultFile.Elements() != null)
        {
            if (defaultFile.Descendants().Count() != customerFile.Descendants().Count())
                return true;
        }
        return false;
    }



    public XDocument SaveDifferences(XDocument differences, string filePathName)
    {

        differences.Save(filePathName);

        return differences;
    }

    public List<string> GetStructuresListToTreat(string environment)
    {
        StructuresList = new();

        string[] files = Directory.GetDirectories("\\\\Srv-paiement64\\customerfiles\\" + environment + "\\");

        foreach (string file in files){

            string folder = Path.GetFileName(file);
            if (folder.All(Char.IsNumber))
            {
                StructuresList.Add(folder);
            } 
            else
            {
                //Do nothing
            }

        }

        return StructuresList;

    }

    public void PrintEachCustomerDifferences(XDocument defaut, XDocument customer, string env)
    {

        if (IsDifferent(defaut, customer))
        {
            GetStructuresListToTreat(env);

            foreach (string str in StructuresList)
            {
                string filePath = "\\\\Srv-paiement64\\customerfiles\\" +env+ "\\" + str + "\\CONFIGSERVER\\config.ini.xml";

                if (File.Exists(filePath))
                {
                    customer = XDocument.Load(filePath);            

                    string diffFilePath = $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\ConfigIniKeysDifferences{env}\\ {str}.ini.xml";
                    CompareFiles(customer, defaut, diffFilePath);
                }
                else
                {
                    MissingConfigIniFile.Add(str);
                }                

            }

        }
    }

    public void GetScriptDifferences()
    {

    }




    /// <summary>
    /// Fusion de toutes les clés clients dans un environnement
    /// Fusion entre ConfigIni du Client et celui du DifferencFile
    /// Retourne toutes les clés du client présentes dans le default ou pas
    /// </summary>
    public void GetConfigIniFusion(XDocument customer, XDocument diffFile)
    {

        XDocument xmlDoc = new XDocument(new XDeclaration("1.0", "iso-8859-1", "yes"));
        XElement root = new XElement("configIni");
        XElement newSection = null;
        XAttribute newAttribute = null;

        ///Total clés clients
        if (customer.Descendants("Section").Elements().Count() > 1)
        {
            foreach (var item in diffFile.Descendants("Section").Attributes("Name"))
            {
                //int sectionsNumber = customer.Descendants("Section").Attributes("Name").Count();

                var sectionName = item.Value;

                var clientElements = from elements in customer.Descendants("configIni").Elements("Section")
                                     where (string)elements.Attribute("Name") == sectionName
                                     select elements;

                var diffElements = from de in diffFile.Descendants("configIni").Elements("Section")
                                   where (string?)de.Attribute("Name") == sectionName
                                   select de;
                //Ajout Section
                newSection = new XElement("Section");
                newAttribute = new XAttribute("Name", sectionName);
                newSection.Add(newAttribute);

                // //Si la Section n'est pas vide
                if (clientElements.Elements().Count() != 0)
                {
                    //Cles clients
                    foreach (var child in clientElements.Elements())
                    {
                        //Si la clés est présente dans le default
                        var childExist = diffElements.Elements().Where(a => a.Name.LocalName.ToUpper() == child.Name.LocalName.ToUpper()).FirstOrDefault();

                        if (childExist == null)
                        {
                            newSection.Add(child);
                        }
                        else
                        {
                            //ajout de l'enfant 
                            newSection.Add(child);
                        }
                    }

                    // //Pour chaque clé du diff absent dans le customer ajour dans newsection
                    // foreach (var k in  clientElements.Elements())
                    // {
                    //     var dif = diffElements.Elements().Where(a => a.Name.LocalName.ToUpper() == k.Name.LocalName.ToUpper()).FirstOrDefault();
                    //     if (dif == null)
                    //     {
                    //         newSection.Add(k);
                    //     }

                    // }
                }

                else
                {
                    // Ajout de la section absente du client
                    foreach (var child in diffElements.Elements())
                    {              
                        newSection.Add(child);
                        // var di = clientElements.Elements().Where(a => a.Name.LocalName.ToUpper() == child.Name.LocalName.ToUpper()).FirstOrDefault();
                        // if (di == null)
                        // {
                        //     newSection.Add(child);
                        // }                    
                    }
                }

                root.Add(newSection);
            }


            //Traiter Root et customer pour récupérer les sections et clés absentes 
            foreach (var item in customer.Descendants("Section").Attributes("Name"))
            {
                var customerSectionName = item.Value;

                var customerElements = from elements in customer.Descendants("configIni").Elements("Section")
                                       where (string)elements.Attribute("Name") == customerSectionName
                                       select elements;

                var rootElements = from de in root.Elements("Section")
                                   where (string?)de.Attribute("Name") == customerSectionName
                                   select de;

                //Chercher si la section sinon l'ajouter
                int existeEl = rootElements.Elements().Count();
                if (rootElements.Elements().Count() == 0)
                {
                    //Ajout Section
                    newSection = new XElement("Section");
                    newAttribute = new XAttribute("Name", customerSectionName);
                    newSection.Add(newAttribute);
                    foreach (var itemcust in customerElements.Elements())
                    {
                        newSection.Add(itemcust);
                    }

                    root.Add(newSection);
                }
                
            }
            
            xmlDoc.Add(root);

            //Print Differences and save file
            //SaveDifferences(xmlDoc, "D:\\FUSION\\FUSION.ini.xml"); $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\ConfigIniKeysDifferencesDEV\\FUSION_DEV.ini.xml"
            //SaveDifferences(xmlDoc, $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\FUSION.ini.xml");
            SaveDifferences(xmlDoc, $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\FINAL\\FUSION.ini.xml");

        }

    }

    public XElement GetCustomerMissedElements(XElement root)
    {

        return root;
    }

    public void GetDifferencesFusion()
    {       

        string[] files = Directory.GetFiles($"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\");         

        foreach (string file in files)
        {
            string diffFile = "D:\\FUSION\\FUSION.ini.xml";           

            if (Path.GetFileName(file) != Path.GetFileName(diffFile))
            {
                var cust = XDocument.Load(file);
                var diff = XDocument.Load(diffFile);
                if (cust.Elements() is not null)
                {
                    GetConfigIniFusion(cust, diff);
                }

            }

        }
    }


    public void FusionEnvironmentKeys()
    {
        // string[] files = Directory.GetFiles($"D:\\TST_SCRIPT_CUSTOMER_KEYS\\ConfigIniKeysDifferencesPROD\\");
        // string[] fusionFiles = Directory.GetFiles($"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\");
        string[] finalfusionFiles = Directory.GetFiles($"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\FINAL\\");

        foreach (string file in finalfusionFiles)
        {
            // string difPath = $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\ConfigIniKeysDifferencesPROD\\FUSION_PROD.ini.xml";
            // string difPathFusion = $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\FUSION.ini.xml";
            string finaldifPathFusion = $"D:\\TST_SCRIPT_CUSTOMER_KEYS\\FUSION\\FINAL\\FUSION.ini.xml";

            if (Path.GetFileName(file) != Path.GetFileName(finaldifPathFusion))
            {
                var dynamicCustomer = XDocument.Load(file);
                var diff = XDocument.Load(finaldifPathFusion);

                if (dynamicCustomer.Elements() is not null)
                {
                    GetConfigIniFusion(dynamicCustomer,diff);
                }

            }

        }
       
    }



    public int KeyCount(string filePath)
    {
        if (filePath.Contains("DEV"))
        {
            DevMissingKeysNumber++;

            return DevMissingKeysNumber;

        }
        else if (filePath.Contains("TEST"))
        {
            TestMissingKeysNumber++;

            return TestMissingKeysNumber;

        }else
        {
            ProdMissingKeysNumber++;
            return ProdMissingKeysNumber;
        }

        return 0; 
    }

}


    