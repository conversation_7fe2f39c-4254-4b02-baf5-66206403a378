﻿
<div class="modal fade show" id="myModal" style="display:block; background-color: rgba(10,10,10,.8);" aria-modal="true" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">@Title</h4>
                <button type="button" class="close" @onclick="@ModalCancel">&times;</button>
            </div>
            <div class="modal-body">
                <p>@Text</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-success" @onclick="@CallParentApplyMethode">Save Changes</button>
                <button type="button" class="btn btn-outline-danger" @onclick="@ModalCancel">Close</button>
            </div>
        </div>
    </div>
</div>


@code {

    [Parameter] public dynamic Parent { get; set; } 
    [Parameter] public RenderFragment ChildContent { get; set; }
    [Parameter] public string Title { get; set; }
    [Parameter] public string Text { get; set; }
    [Parameter] public EventCallback<bool> OnClose { get; set; }

    private Task ModalCancel()
    {
        return OnClose.InvokeAsync(false);
    }

    private Task ModalOk()
    {
        return OnClose.InvokeAsync(true);
    }

    private void CallParentApplyMethode()
    {
        // if (Parent.Apply())
        // {
        //     ModalCancel();   
        // }
    }
}
