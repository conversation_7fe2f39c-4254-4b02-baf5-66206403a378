﻿using System.ComponentModel.DataAnnotations;

namespace MiseEnVentes.Models
{

    public class EditFormModel
    {
        public DateTime Date { get; set; } = DateTime.Today;
        public string? Heure { get; set; }
        public List<int> Plateformes { get; set; } = new List<int>();
        public string? Structure { get; set; }
        public List<int> Intervenants { get; set; } = new List<int>();
        public bool Mail { get; set; }
        public bool Ticket { get; set; }
        [StringLength(300, ErrorMessage = "Le commentaire ne doit pas dépasser 300 caractères.")]
        public string? Commentaire { get; set; } = null;
        public ImportanceNiveau? Importance { get; set; } = ImportanceNiveau.Normal;
    }
}
