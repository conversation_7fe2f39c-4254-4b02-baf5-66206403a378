﻿using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.Eventing.Reader;
using static MiseEnVentes.Pages.IndexBase;


namespace MiseEnVentes.Pages 
{
    public partial class Intervenants : ComponentBase
    {

        public List<Intervenant>? intervenants;
        public List<MiseEnVenteIntervenant>? MiseEnVenteIntervenants;
        public AddFormModeIntervenant formAddModel = new();

        public bool showModalAdd_Intervenant = false;
        public bool showDeleteModal = false;
        public Intervenant? IntervenantToDelete;

        public string message = string.Empty;

        public Intervenant? IntervenantToEdit;
        public bool showModalEdit_Intervenant = false;
        public EditFormModel_for_Intervenant editModel_inter = new();
        public bool showEditModal = false;

        public bool showToast = false;
        public bool showEditToast = false;
        public bool showDeleteToast = false;

        protected int? lastModifiedId = null;


        protected override async Task OnInitializedAsync()
        {
            intervenants = await Db.Intervenants
                .OrderBy(i => i.IntervenantName)
                .ToListAsync();
        }

        // pour afficher le modal d'ajout d'un Intervenant
        protected void Modal_Add()
        {
            showModalAdd_Intervenant = !showModalAdd_Intervenant;
            if (showModalAdd_Intervenant == false)
                formAddModel = new(); // Reset si on ferme
        }

        // Methode pour ajouter un Intervenant
        public async Task AjouterIntervenant()
        {
            bool existe = intervenants!.Any(i => i.IntervenantName == formAddModel.IntervenantName);

            if (existe)
            {
                message = "Cet utilisateur existe déjà.";
                return;
            }

            var nouvelIntervenant = new Intervenant
            {
                IntervenantName = formAddModel.IntervenantName,
                IntervenantEmail = formAddModel.IntervenantEmail
            };

            Db.Intervenants.Add(nouvelIntervenant);
            await Db.SaveChangesAsync();
            lastModifiedId = nouvelIntervenant?.IdIntervenant;

            intervenants?.Add(nouvelIntervenant);
            intervenants = intervenants?
                .OrderBy(i => i.IntervenantName)
                .ToList();

            showModalAdd_Intervenant = false;
            formAddModel = new();
            await ShowToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();
        }


        // Methode pour supprimer un intervenant
        protected async Task DeleteIntervenant()
        {
            if (IntervenantToDelete is not null)
            {
                var liens = await Db.MiseEnVenteIntervenants
                    .Where(x => x.IdIntervenant == IntervenantToDelete.IdIntervenant)
                    .ToListAsync();

                Db.MiseEnVenteIntervenants.RemoveRange(liens);

                Db.Intervenants.Remove(IntervenantToDelete);
                await Db.SaveChangesAsync();
                intervenants = await Db.Intervenants.ToListAsync();

                IntervenantToDelete = null;
                showDeleteModal = false;
                await ShowDeleteToast();
            }
        }

        protected void ConfirmDelete(Intervenant intervenant)
        {
            IntervenantToDelete = intervenant;
            showDeleteModal = true;
        }

        protected void CancelDelete()
        {
            showDeleteModal = false;
            IntervenantToDelete = null;
        }



        // Ouvre le modal pour modifier un intervenant
        protected void OpenEditModal_for_intervenant(Intervenant intervenant)
        {
            IntervenantToEdit = intervenant;

            editModel_inter = new EditFormModel_for_Intervenant
            {
                IntervenantName = intervenant.IntervenantName,
                IntervenantEmail = intervenant.IntervenantEmail
            };

            showModalEdit_Intervenant = true;
        }

        protected void CloseEditModal()
        {
            showModalEdit_Intervenant = false;
            IntervenantToEdit = null;
        }

        // Methode pour modifier un intervenant
        protected async Task EditIntervenant()

        {
            if (IntervenantToEdit is not null)
            {
                IntervenantToEdit.IntervenantName = editModel_inter.IntervenantName;
                IntervenantToEdit.IntervenantEmail = editModel_inter.IntervenantEmail;

                await Db.SaveChangesAsync();
                lastModifiedId = IntervenantToEdit?.IdIntervenant;
            }

            showModalEdit_Intervenant = false;
            IntervenantToEdit = null;
            await ShowEditToast();
            await Task.Delay(2000); // 2 secondes
            lastModifiedId = null;
            StateHasChanged();

        }

        // Toast pour prévenir de l'action effectuer
        protected async Task ShowToast()
        {
            showToast = true;
            StateHasChanged(); // Affiche immédiatement

            await Task.Delay(5000);
            showToast = false;
            StateHasChanged(); // Cache le toast après 5s
        }

        protected void HideToast()
        {
            showToast = false;
            showEditToast = false;
            showDeleteToast = false;
        }

        protected async Task ShowEditToast()
        {
            showEditToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showEditToast = false;
            StateHasChanged();
        }

        protected async Task ShowDeleteToast()
        {
            showDeleteToast = true;
            StateHasChanged();

            await Task.Delay(5000);
            showDeleteToast = false;
            StateHasChanged();
        }


    }
}
