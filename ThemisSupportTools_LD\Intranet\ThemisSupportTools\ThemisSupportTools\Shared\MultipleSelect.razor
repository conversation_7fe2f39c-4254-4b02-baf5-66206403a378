﻿<div class="col-sm-10">
    <select multiple class="form-select mb-2">
        @foreach (var item in SelectItemsLookup)
        {
            backgroundColorCode = "";
            if (item.IsSelected)
                backgroundColorCode = "bg-info";

            <option value="@item.Value" class="@backgroundColorCode" @onclick=@((e) => OptionClickEvent(item,e))>@item.Libelle</option>

        }
    </select>
    <div class="d-flex gap-1">
        @foreach (var holderItem in SelectLookupSelected)
        {

            <span class="badge text-bg-secondary">
        @*<button class="oi oi-x"></button>*@
        @holderItem.Libelle</span>
           
        }
    </div>
</div>
    @code {

        [Parameter] public string PlaceHolder { get; set; } = string.Empty;
        [Parameter] public List<SelectLookup> SelectItemsLookup { get; set; } = new();
        [Parameter] public List<SelectLookup> SelectLookupSelected { get; set; } = new();

        private List<string> myHolder = new List<string>();
        private string backgroundColorCode = "";

        protected override void OnInitialized()
        {
            SelectLookupSelected = SelectItemsLookup.Where(s => s.IsSelected).ToList();

        }

        public void OptionClickEvent(SelectLookup values, MouseEventArgs evnt)
        {
            if (evnt.CtrlKey)
            {
                AddSelectLookupVariablesSelected(values);
            }
            else
            {
                AddSelectLookupVariablesSelected(values);
            }
        }


        private void AddSelectLookupVariablesSelected(SelectLookup selectLookup)
        {
            if (!SelectLookupSelected.Any(v => v.Libelle == selectLookup.Libelle))
            {
                SelectLookupSelected.Add(selectLookup);
                selectLookup.IsSelected = true;
            }
            else
            {
                SelectLookupSelected.Remove(selectLookup);
                selectLookup.IsSelected = false;
            }

        }
        //public void OptionClickEvent(string values, MouseEventArgs evnt)
        //{
        //    myHolder.Add(values);
        //}

        //private async Task<List<GenericSelectDataLookup>> GetStructures()
        //{
        //    return  await WsAdminStructuresManager.GetSelectLookupStructures();
        //}

        //    protected override async Task OnInitializedAsync()
        //    {
        //        SelectItems = new();
        //    }


    }
