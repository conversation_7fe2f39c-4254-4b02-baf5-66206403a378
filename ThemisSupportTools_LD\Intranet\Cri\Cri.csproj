﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Cri</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="--Ressources\**" />
    <Compile Remove="Pages\Utilisateur\Inscription\**" />
    <Content Remove="--Ressources\**" />
    <Content Remove="Pages\Utilisateur\Inscription\**" />
    <EmbeddedResource Remove="--Ressources\**" />
    <EmbeddedResource Remove="Pages\Utilisateur\Inscription\**" />
    <None Remove="--Ressources\**" />
    <None Remove="Pages\Utilisateur\Inscription\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Pages\Shared\TestLDAP.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Pages\Utilisateur\Modal.razor" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Pages\Shared\load_action.cs" />
    <Content Include="Pages\Shared\save.cs" />
    <Content Include="Pages\Utilisateur\deconnexion.cshtml.cs" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Model\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Localization" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Session" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="MySql.Data" Version="9.1.0" />
    <PackageReference Include="MySql.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="QuestPDF" Version="2024.10.4" />
    <PackageReference Include="Web.UI.WebControls" Version="1.0.2.226" />
  </ItemGroup>

  <ItemGroup>
    <UpToDateCheckInput Remove="Pages\Utilisateur\Modal.razor" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Pages\Utilisateur\Modal.razor" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Ressources\Ressource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Ressource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Ressources\Ressource.de.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Ressources\Ressource.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Ressources\Ressource.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Ressource.Designer.cs</LastGenOutput>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Update="Ressources\Ressource.fr.resx">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <Generator>PublicResXFileCodeGenerator</Generator>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

</Project>
