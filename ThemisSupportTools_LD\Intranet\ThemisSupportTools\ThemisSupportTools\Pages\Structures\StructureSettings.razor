﻿@page "/structureSettings/{structureId}/{environment}"

@attribute [Authorize]

@using Core.Themis.Librairies.Razor;
@using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
@using System.Xml;
@using System.Xml.Linq;
@using ThemisSupportTools.Pages.ModalDialogComponent;
@using ThemisSupportTools.Pages.Structures;
@using System.Xml.Serialization;
@using System.Collections.Generic;
@using ThemisSupportTools.Pages.TypesComponents;
@using ThemisSupportTools.Data;
@using Core.Themis.Librairies.Razor.ConfigIni;
@using System.Timers;
@using Microsoft.AspNetCore.Identity;

@inject IJSRuntime jsRuntime
@inject IJSRuntime js
@inject NavigationManager navigationManager


<div class="">

    <h3 style="text-align: center">STRUCTURE : @structureId (@environment) </h3>
        
    <div class="container" style="padding:7px">     

        <Alert  Color="AlertColor.Danger"> <Icon Name="IconName.ExclamationTriangleFill" class="me-2"></Icon><strong>Time Left : @TimeLeft</strong></Alert>

        <div class="col border border-2">

            <form class="d-flex" role="search" style="padding:7px">

                <SearchBox Model="monModels" OnSearch="UpdateConfigIniForm"></SearchBox>

                @* <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search"
                           @bind="Filter" @bind:event="oninput"></input>
                 <button class="btn btn-outline-success" type="submit">Search</button> *@
            </form>

          @*   <div class="col-auto border border-light" style="padding:7px">
                <InputText type="text" list="sectionSeach" class="form-select" area-label="Choix Filtre" id="sectionFilter" Value="@SelectedSection"
                             ValueChanged="@((string value) => OnChangedSection(value))" ValueExpression="@(()=>SelectedSection)" placeholder="Filtre par section ..." >   </InputText>
                <datalist id="sectionSeach" >
                    <option value="-1" > --- Toutes les sections sections --- </option> 
                    @foreach (var sec in sections)
                    {
                        <option value="@sec.SectionName">@sec.SectionName</option>
                    }
                </datalist>
            </div> *@

            <div class="col-auto border border-light" style="padding:7px">
                <label>Filtre Groupe de clés :</label>
                <InputSelect class="form-select" area-label="Choix Filtre" id="sectionFilter" Value="@SelectedSection"
                             ValueChanged="@((string value) => OnChangedSection(value))" ValueExpression="@(()=>SelectedSection)">

                    <option value="-1"> --- Choix Section --- </option>
                    @foreach (var sec in sections)
                    {
                        <option value="@sec.SectionName">@sec.SectionName</option>
                    }
                </InputSelect>
            </div>

            <div class="col-auto" style="padding:7px">
                <button class="btn btn-outline-success" type="submit" @onclick="ShowSaveConfirmationAsync">Sauvegarder</button>
                <button class="btn btn-outline-danger" @onclick="ShowRestoreConfirmationAsync "> Restauration</button>
                <button class="btn btn-outline-secondary" href="" @onclick="DeleteTmpFileOnChangeState"> Changer de structure</button>                          
            </div>
        </div>

    </div>


    <div class="container" style="padding:7px">

        <ConfirmDialog @ref="dialog" />
        <PopUp @ref="popup"></PopUp>

        <div class="row">

            <EditForm Model="monModels">

                <div class="accordion border border-1" id="settings_sections_customer_wrapper">
                    @{
                        cpt = 0;
                        foreach (var item in monModels)
                        {
                            // if (!IsVisibleString(item.SectionName))
                            //     continue;

                            <div class="settings_section" data-id="@item.SectionName">
                                <h2 class="accordion-header border border-2 " id="heading_customer_@cpt">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#collapse_customer_@cpt" aria-expanded="true" aria-controls="collapse_customer_@cpt">
                                        @item.SectionName
                                    </button>
                                </h2>

                                <div id="collapse_customer_@cpt" class="accordion-collapse collapse show" aria-labelledby="heading_customer_@cpt"
                                     data-bs-parent="#accordion_customer">
                                    <div class="accordion-body">
                                        <div class="row" data-id="@item.SectionName">
                                            @foreach (var child in item.Children)
                                            {
                                                // if (!IsVisibleString(child.ChildKeyName))
                                                //     continue;

                                                ClassName = "border-danger";
                                                FieldClassName = "is-invalid";

                                                if (!string.IsNullOrEmpty(child.ChildKeyValue))
                                                {
                                                    ClassName = "border-success";
                                                    FieldClassName = "is-valid";

                                                }

                                                var inputType = child.Attributes.FirstOrDefault(c => c.AttributeName.ToLower() == ("type"))?.AttributeValue ?? "text";
                                                var min = child.Attributes.FirstOrDefault(c => c.AttributeName.ToLower() == ("min"))?.AttributeValue;
                                                var max = child.Attributes.FirstOrDefault(c => c.AttributeName.ToLower() == ("max"))?.AttributeValue;
                                                var valeurs = child.Attributes.FirstOrDefault(c => c.AttributeName.ToLower() == ("valeur"))?.AttributeValue;
                                                var comments = child.Attributes.FirstOrDefault(c => c.AttributeName.ToLower() == ("commentaire"))?.AttributeValue;

                                                <div class="col-12 settings_section_item border-3 border-start p-2 @ClassName" data-parent="@item.SectionName">

                                                    @if (inputType == "select")
                                                    {

                                                        int minSelect = Convert.ToInt32(min);
                                                        int maxSelect = Convert.ToInt32(max);

                                                        @if (minSelect >= 0 && maxSelect > minSelect)
                                                        {
                                                            inputSelectItems = new();

                                                            for (int i = minSelect; i <= maxSelect; i++)
                                                            {

                                                                InputSelectModel inputList = new();
                                                                inputList.Name = i.ToString();
                                                                inputList.Value = i.ToString();

                                                                inputSelectItems.Add(inputList);
                                                            }

                                                            <div class="form-control ">

                                                                <label class="form-label">@child.ChildKeyName</label>
                                                                <Tooltip Title="@comments" Color="TooltipColor.Danger" role="button">
                                                                    <Icon Name="IconName.InfoCircleFill" Color="IconColor.Info"></Icon>
                                                                </Tooltip>
                                                                <select class="@($"form-control {FieldClassName}")" @bind="child.ChildKeyValue" id="minMaxValue">
                                                                    <option value="" selected>@KeyNotUsed</option>
                                                                    @foreach (var inputs in inputSelectItems)
                                                                    {
                                                                        <option value="@inputs.Value">@inputs.Name</option>
                                                                    }
                                                                </select>


                                                            </div>

                                                            // <DynamicSelect Label="@child.ChildKeyName" SelectedValue="@child.ChildKeyValue" InputSelectItems="@inputSelectItems" PlaceHolder="@KeyNotUsed" CssClass="@ClassName" FieldCssClass="@FieldClassName" />
                                                            // <DynamicInputSelect Label="@child.ChildKeyName" SelectedValue="@child.ChildKeyValue" InputSelectItems="@inputSelectItems" PlaceHolder="@KeyNotUsed" CssClass="@ClassName" FieldCssClass="@FieldClassName" />

                                                        }
                                                        else if (valeurs != null)
                                                        {
                                                            inputSelectItems = new();
                                                            List<string> checkValeurs = valeurs.Split(",").ToList();

                                                            for (int i = 1; i <= checkValeurs.Count(); i++)
                                                            {
                                                                InputSelectModel inputListValue = new();
                                                                inputListValue.Name = checkValeurs[i - 1].ToString();
                                                                inputListValue.Value = checkValeurs[i - 1].ToString();

                                                                inputSelectItems.Add(inputListValue);
                                                            }

                                                            <div class="form-control">

                                                                <label class="form-label">@child.ChildKeyName</label>
                                                                <Tooltip Title="@comments" Color="TooltipColor.Danger" role="button">
                                                                    <Icon Name="IconName.InfoCircleFill" Color="IconColor.Info"></Icon>
                                                                </Tooltip>
                                                                <select class="@($"form-control {FieldClassName}")" @bind="child.ChildKeyValue" id="valueList">
                                                                    <option value="" selected>@KeyNotUsed</option>
                                                                    @foreach (var input in inputSelectItems)
                                                                    {
                                                                        <option value="@input.Value">@input.Name</option>
                                                                    }
                                                                </select>

                                                            </div>

                                                            // <DynamicSelect Label="@child.ChildKeyName"  SelectedValue="@child.ChildKeyValue" InputSelectItems="@inputSelectItems" PlaceHolder="@KeyNotUsed" CssClass="@ClassName" FieldCssClass="@FieldClassName" />
                                                            // <DynamicInputSelect Label="@child.ChildKeyName" SelectedValue="@child.ChildKeyValue" InputSelectItems="@inputSelectItems" PlaceHolder="@KeyNotUsed" CssClass="@ClassName" FieldCssClass="@FieldClassName" />

                                                        }

                                                    }
                                                    else
                                                    {
                                                        <DynamicInput Label="@child.ChildKeyName" Type="@inputType" @bind-value="@child.ChildKeyValue" PlaceHolder="@KeyNotUsed" CssClass="@ClassName" FieldCssClass="@FieldClassName" Comment="@comments" />

                                                    }


                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            cpt++;
                        }
                    }

                </div>

            </EditForm>

        </div>
    </div>


</div>


@code {

    [CascadingParameter]
    private Task<AuthenticationState> authenticationState { get; set; }

    private string SearchTerm = string.Empty;

    public string CssClass { get; set; } = "form-control";

    public string ClassName { get; set; } = "border-danger ";

    public string FieldClassName { get; set; } = "is-invalid";

    private int cpt = 0;

    private string KeyNotUsed = "Clé non utilisée";

    public string SelectedSection { get; set; } = string.Empty;

    public string Filter { get; set; } = string.Empty;

    public string SelectValue { get; set; } = string.Empty;

    [Inject] private IThemisSupportToolsManager _themisSupportToolsManager { get; set; }
    [Inject] private IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary { get; set; }

    [Parameter] public string structureId { get; set; } = string.Empty;

    [Parameter] public string environment { get; set; } = string.Empty;

    private IEnumerable<XElement> defaultKeys;

    private IEnumerable<XElement> fusion;

    public List<Sections> sections = new List<Sections>();

    public List<ConfigIniSectionDTO> monModels = new List<ConfigIniSectionDTO>();

    public List<ConfigIniSectionDTO> monModelTemps = new List<ConfigIniSectionDTO>();

    public List<ConfigIniModel> monModelSearch = new ();

    public List<ConfigIniModel> finalCustomerModel = new List<ConfigIniModel>();

    public List<InputSelectModel> inputSelectItems = new List<InputSelectModel>();

    string customerFilePath = string.Empty;

    public bool HasAccess { get; set; } = true;

    private PopUp popup = new();

    private ConfirmDialog dialog = default!;
   
    public string fileUser = string.Empty;



    


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {

        if (firstRender)
        {
            var user = (await authenticationState).User;
            string currentUser = user.Identity.Name;         

            customerFilePath = _rodrigueConfigIniDictionnary.GetCustomerPath(int.Parse(structureId), environment);
            fileUser = _rodrigueConfigIniDictionnary.GetConfigIniUser(customerFilePath);

            @if (_rodrigueConfigIniDictionnary.HasTempFile(customerFilePath, fileUser))
            {
                if (currentUser != fileUser)
                {
                    //Fichier en cours d'utilisation par username: ACCESS DENIED
                    popup.Show("Fichier en cours d'utilisation par " + fileUser.ToUpper() + ", changer de structure !", "Themis Support Tools");
                    HasAccess = false;

                } else
                {
                    //Suppression Fichier Temp et Continue
                    // Chargement, Dévelopement
                    defaultKeys = _rodrigueConfigIniDictionnary.GetConfigIniDefaultKeys(int.Parse(structureId), environment);
                    sections = GetSections(defaultKeys);
                    fusion = _rodrigueConfigIniDictionnary.GetConfigIniFusion(int.Parse(structureId), environment);

                    GetFusionKeys(fusion);
                    monModelTemps = monModels;

                    //monModelSearch = monModels;
                }                
                
            }
            else
            {
               
                // Création tmpFile
                _rodrigueConfigIniDictionnary.CreateTempFile(customerFilePath, currentUser);

                // Chargement, Dévelopement
                defaultKeys = _rodrigueConfigIniDictionnary.GetConfigIniDefaultKeys(int.Parse(structureId), environment);
                sections = GetSections(defaultKeys);
                fusion = _rodrigueConfigIniDictionnary.GetConfigIniFusion(int.Parse(structureId), environment);

                GetFusionKeys(fusion);
                monModelTemps = monModels;

                //monModelSearch = monModels;

            }

            Timer();
            StateHasChanged();
        }

    }

    // protected override void OnInitialized()
    // {

    // }

    void UpdateConfigIniForm(string searchItem)
    {
        if (!string.IsNullOrEmpty(searchItem))
        {
            // monModels = monModelTemps;

            monModelSearch = monModels.Where(

                model => model.Children.Any(child => child.ChildKeyName.ToLower().Contains(searchItem.ToLower()))

                )                
                .ToList();

            monModels = monModelSearch;
        }


    }


    private async Task ShowSaveConfirmationAsync()
    {
        var options = new ConfirmDialogOptions
            {
                YesButtonText = "SAUVEGARDER",
                YesButtonColor = ButtonColor.Success,
                NoButtonText = "ANNULER",
                NoButtonColor = ButtonColor.Danger
            };

        var confirmation = await dialog.ShowAsync(
            title: "Themis Support Tools",
            message1: "Etes-vous sûr de vouloir sauvegarder les modifications ?",
            confirmDialogOptions: options);

        if (confirmation)
        {
            // do something
            monModels = monModelTemps;
            CreateFinalXml(monModels);
        }
        else
        {
            // do nothing
        }
    }

    void DeleteTmpFileOnChangeState()
    {
        //Suppression fichier temporaire
        _rodrigueConfigIniDictionnary.DeleteTempFile(customerFilePath);

        //Redirection HomePage
        navigationManager.NavigateTo($"");
    }

    protected override async void OnAfterRender(bool firstRender)
    {

        if (firstRender)
        {
            customerFilePath = _rodrigueConfigIniDictionnary.GetCustomerPath(int.Parse(structureId), environment);

            @if (_rodrigueConfigIniDictionnary.HasTempFile(customerFilePath))
            {
                //Fichier en cours d'utilisation par username: ACCESS DENIED
                popup.Show("Fichier en cours d'utilisation, changer de structure !", "Themis Support Tools, Warning ");
                HasAccess = false;
            }
            else
            {
               
                // Création tmpFile
                _rodrigueConfigIniDictionnary.CreateTempFile(customerFilePath);

                // Chargement, Dévelopement
                defaultKeys = _rodrigueConfigIniDictionnary.GetConfigIniDefaultKeys(int.Parse(structureId), environment);
                fusion = _rodrigueConfigIniDictionnary.GetConfigIniFusion(int.Parse(structureId), environment);


                sections = GetSections(defaultKeys);
               monModels =  _themisSupportToolsManager.GetFusionKeys(fusion);
                monModelTemps = monModels;

            }


            Timer();

            StateHasChanged();
        }

    }


    //public List<ConfigIniModel> GetFusionKeys(IEnumerable<XElement> fusion)
    //{
    //    foreach (var item in fusion)
    //    {
    //        ConfigIniModel m = new();
    //        m.SectionName = item.Attribute("Name").Value;
    //        m.Children = new();

    //        //Pour chaque enfant de la section
    //        foreach (var child in item.Elements())
    //        {

    //            ChildModel modelChild = new ChildModel();
    //            modelChild.ChildKeyName = child.Name.ToString();
    //            modelChild.ChildKeyValue = child.Value;

    //            m.Children.Add(modelChild);

    //            //Pour chaque attribut de l'enfant
    //            foreach (var attribute in child.Attributes())
    //            {
    //                AttributeModel attributeModel = new AttributeModel()
    //                {
    //                    AttributeName = attribute.Name.ToString(),
    //                    AttributeValue = attribute.Value,
    //                };

    //                    AttributeModel.SetAttributeType(attribute.Name.ToString());
    //                modelChild.Attributes.Add(attributeModel);
    //            }

    //        }

    //        monModels.Add(m);
    //    }

    //    return monModels;
    //}

    public bool IsVisibleString(string section)
    {
        if (string.IsNullOrEmpty(Filter))
            return true;

        if (section.ToUpper().Contains(Filter.ToUpper()))
            return true;

        return false;
    }

    public async Task OnChangedSection(string val)
    {
        monModels = monModelTemps;
        if (val != "-1")
        {
            monModels = monModels.Where(m => m.SectionName.ToUpper().Equals(val)).ToList();
        }
        else
        {
            monModelTemps = monModels;
        }

    }

    public List<Sections> GetSections(IEnumerable<XElement> list)
    {
        if (list == null)
            throw new ArgumentNullException();

        foreach (var section in list.Attributes("Name"))
        {
            Sections sectionsList = new Sections();
            sectionsList.SectionName = section.Value;

            sections.Add(sectionsList);
        }

        return sections;

    }

    void SelectedSectionChange(ChangeEventArgs sectionEvent)
    {

        SelectedSection = sectionEvent.Value.ToString();
        this.StateHasChanged();

    }

    public List<ConfigIniModel> CreateFinalCustomer(List<ConfigIniModel> model)
    {

        List<ConfigIniModel> modelFinalToSave = new();

        model.ForEach(a =>
        {
            ConfigIniModel m = new();

            a.Children.ForEach(child =>
            {
                if (!string.IsNullOrEmpty(child.ChildKeyValue))
                {
                    //Ajout de clés dans la nouvelle section de l'object
                    if (modelFinalToSave.FirstOrDefault(mo => mo.SectionName.ToUpper().Equals(a.SectionName.ToUpper())) is not null)
                    {
                        var newObjectSection = modelFinalToSave.FirstOrDefault(ma => ma.SectionName.ToUpper().Equals(a.SectionName.ToUpper()));

                        if (newObjectSection is not null)
                        {
                            newObjectSection.Children.Add(child);
                        }
                    }
                    else
                    {
                        //Création section et ajout de clés
                        ConfigIniModel sectionFinale = new();
                        sectionFinale.SectionName = a.SectionName;
                        sectionFinale.Children.Add(child);

                        modelFinalToSave.Add(sectionFinale);
                    }
                }
            });
        });

        return modelFinalToSave;

    }

    public XDocument CreateFinalXml(List<ConfigIniModel> finalCustomerFormModel)
    {

        //Création du model final
        finalCustomerModel = CreateFinalCustomer(finalCustomerFormModel);

        XDocument finalXml = new XDocument();
        XElement root = new XElement("configIni");

        //Conversion du nouveau model en XML
        foreach (var item in finalCustomerModel)
        {
            XElement newSection = new XElement("Section");
            XAttribute newAttribut = new XAttribute("Name", item.SectionName);
            newSection.Add(newAttribut);

            //Enfants de la section
            foreach (var child in item.Children)
            {
                //Créer les enfants avec leurs valeurs
                XElement childNode = new XElement(child.ChildKeyName, child.ChildKeyValue);
                newSection.Add(childNode);

                // //Transfert chaque attribut de l'enfant
                // foreach (var ItemChildAttribut in child.Attributes)
                // {
                //     XAttribute childAttribut = new XAttribute(ItemChildAttribut.AttributeName, ItemChildAttribut.AttributeValue);
                //     childNode.Add(childAttribut);
                // }

            }

            root.Add(newSection);

        }

        finalXml.Add(root);


        /***** SAUVEGARDE FINAL CUSTOMER ***/
        SaveFinalXml(finalXml, customerFilePath);

        return finalXml;
    }

    public void SaveFinalXml(XDocument finalCustomer, string finalCustomerPath)
    {

        //Check si le config.ini du client existe alors on sauvegarde
        if (File.Exists(customerFilePath))
        {
            //BackUp Folder
            string backUpFolderName = "__ConfigIniBackup";

            //if HasBackupFolder,
            if (_rodrigueConfigIniDictionnary.HasBackUpFolder(customerFilePath, backUpFolderName))
            {
                //Copie du configinI avec versionning(toujours derniere version)
                string destination = Path.GetDirectoryName(customerFilePath) + "\\" + backUpFolderName;
                _rodrigueConfigIniDictionnary.CreateBackUpFile(customerFilePath, destination);

            }
            else
            {
                //Création et Copie configIni avec sa versionning
                _rodrigueConfigIniDictionnary.CreateBackUpFolder(customerFilePath, backUpFolderName);

            }
        }

        else
        {
            Directory.CreateDirectory(Path.GetDirectoryName(customerFilePath));
        }


        //Enregistrement formulaire
        finalCustomer.Save(finalCustomerPath);

        //Suppression fichier temporaire
        _rodrigueConfigIniDictionnary.DeleteTempFile(customerFilePath);

        //Popup Validation "Mise à jour avec succès" Redirection Home page 
        popup.Show("Sauvegarde avec Succès, Well done!", "Themis Support Tools");

    }

    //Restauration
    private async Task ShowRestoreConfirmationAsync()
    {
        var options = new ConfirmDialogOptions
            {
                YesButtonText = "RESTAURER",
                YesButtonColor = ButtonColor.Success,
                NoButtonText = "ANNULER",
                NoButtonColor = ButtonColor.Danger
            };

        var confirmation = await dialog.ShowAsync(
            title: "Themis Support Tools",
            message1: "Etes-vous sûr de vouloir restaurer le dernier fichier sauvegardé ?",
            confirmDialogOptions: options);

        if (confirmation)
        {
            // do something
            List<ConfigIniModel> restoreConfigIni = new List<ConfigIniModel>();
            var backupXmlFilePath = _rodrigueConfigIniDictionnary.GetLastBackUpFile(customerFilePath, "__ConfigIniBackup");
            var defaultXmlPath = _rodrigueConfigIniDictionnary.GetDefaultPath(int.Parse(structureId), environment);
            fusion = _rodrigueConfigIniDictionnary.GetConfigIniBackUp(defaultXmlPath, backupXmlFilePath);
            monModels = new();
            _themisSupportToolsManager.GetFusionKeys(fusion);
        }
        else
        {
            // do nothing
        }
    }


    TimeSpan TimeLeft = new TimeSpan(4, 0, 00);
    string displayText = "";

    async Task Timer()
    {
        while(TimeLeft > new TimeSpan())
        {
            await Task.Delay(1000);
            TimeLeft = TimeLeft.Subtract(new TimeSpan(0, 0, 1));
            StateHasChanged();
           
        }
        await AfterTime();
        StateHasChanged();
    }

    Task AfterTime()
    {
        displayText = "Temps expiré. Nous allons vous rediriger vers la page d'accueil";
        _rodrigueConfigIniDictionnary.DeleteTempFile(customerFilePath);
        
        //Redirection HomePage
        navigationManager.NavigateTo($"");
        return Task.CompletedTask;

    }

    public void CloseWindow()
    {

        js.InvokeVoidAsync("window.close");
    }

   


}

