﻿
using Core.Themis.Libraries.BLL.InfoComp.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Queue.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.smtpMessages;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using static Core.Themis.API.External.Helpers.ActivetrailObjects;

namespace Core.Themis.API.External.Controllers
{
    /// <summary>
    ///  Activetrail WebHook
    /// </summary>
    [ApiController]
    [SwaggerTag("internal")]
    [ApiExplorerSettings(GroupName = "internal")]
    public class ActivetrailController : ControllerBase
    {

        private readonly IConfiguration _configuration;

        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IInfoCompManager _infoCompManager;

        private static readonly RodrigueNLogger Logger = new();
        private readonly IMemoryCache _memoryCache;
        private readonly IIdentiteManager _identiteManager;

        public ActivetrailController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IInfoCompManager infoCompManager,
            IIdentiteManager identiteManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;

            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _infoCompManager = infoCompManager;
            _identiteManager = identiteManager;
        }

        /// <summary>
        /// Mets à jour de l'infocomp avec le nouvel evenement du mail => appelé quand internaute remplis un formulaire activeTrail
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="groupeId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpPost]
        [Route("api/Activetrailwebhook/{key}/{structureId}/{groupeId}")]
        public async Task<IActionResult> ActivetrailWebHook(int structureId, string key, string groupeId, [FromBody] ContactItem item)
        {
            try
            {

                string ActivetrailWebHoookKey = _configuration["ActivetrailWebHoookKey"];
                if (key != ActivetrailWebHoookKey)
                {
                    Logger.Error(structureId, $" Key {key} is not authorized");
                    return Problem($"Access denied.", null, StatusCodes.Status401Unauthorized);
                }


                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                string emailColumn = configIniXml["VARIABLESEMAIL"].ToString();
                
                if( string.IsNullOrEmpty(item.Email) || string.IsNullOrEmpty(groupeId))
                {
                    string errMsgDetails = $"Email and Group are mandatory. ActivetrailWebHook({structureId}) Email={item.Email} GroupeID={groupeId} error";
                    Logger.Error(structureId, errMsgDetails);
                    return Problem($"Error during updating group in database.  {errMsgDetails}", null, StatusCodes.Status500InternalServerError);
                }

                string pattern = @"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$";

                Regex regex = new Regex(pattern);
                bool isValidEmail = regex.IsMatch(item.Email);

                if (!isValidEmail)
                {
                    string errMsgDetails = $"Email is not valid. ActivetrailWebHook({structureId}) Email={item.Email} GroupeID={groupeId} error";
                    Logger.Error(structureId, errMsgDetails);
                    return Problem($"Error during updating group in database.  {errMsgDetails}", null, StatusCodes.Status500InternalServerError);
                }

                if(!_infoCompManager.GroupExists(structureId, groupeId ))
                {
                    string errMsgDetails = $"The group does not exist. ActivetrailWebHook({structureId}) Email={item.Email} GroupeID={groupeId} error";
                    Logger.Error(structureId, errMsgDetails);
                    return Problem($"Error during updating group in database.  {errMsgDetails}", null, StatusCodes.Status500InternalServerError);
                }


                #region creation identité si email n'existe pas
                if (!_identiteManager.ThisEmailExisteDeja(structureId, item.Email))
                {
                    IdentityDTO identity = new IdentityDTO();
                    identity.Email = item.Email;
                    identity.FirstName = item.FirstName;
                    identity.SurName = item.LastName;
                    identity.MobilePhoneNumber = item.SMS;

                    identity.IdentiteId = _identiteManager.AddIdentity(structureId, identity, false);

                }
                #endregion

                bool ret =  _infoCompManager.UpdateGroup(structureId, item.Email, emailColumn, groupeId);
                if (ret)
                {
                    Logger.Debug(structureId, $"ActivetrailWebHook({structureId})  Email={item.Email} GroupeID={groupeId} ok ");

                    return Ok();
                }
                else
                {
                    string errMsgDetails = $"ActivetrailWebHook({structureId}) Email={item.Email} GroupeID={groupeId} error";
                    Logger.Error(structureId, errMsgDetails);
                    return Problem($"Error during updating group in database. {errMsgDetails}", null, StatusCodes.Status500InternalServerError);
                }

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"ActivetrailWebHook({structureId})  Email={item.Email} GroupeID={groupeId} error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

    }
}
