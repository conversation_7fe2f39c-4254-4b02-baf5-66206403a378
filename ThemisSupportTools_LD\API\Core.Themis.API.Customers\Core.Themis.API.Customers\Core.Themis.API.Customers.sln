﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33627.172
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.API.Customers", "Core.Themis.API.Customers\Core.Themis.API.Customers.csproj", "{DC45FE3B-2E19-4B1D-B116-2AAA34A2D16B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{8B0DFD41-AE30-4CF3-8758-6E5DDAE79A2E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{3CBC8DA6-CAD5-4351-B8D4-D803AB5191CD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{5124FB45-0D83-4DA0-B142-8D71A8238C13}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{34CD5FCA-A16F-4873-923C-8C5B173F1F2C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{EF90B08B-4DBA-4A4D-9B25-D2750810DD12}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{2860755F-CCBD-48C4-8E2D-1347D9C2EA37}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{129D9004-682B-4FFB-813D-6F3894557F12}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DC45FE3B-2E19-4B1D-B116-2AAA34A2D16B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC45FE3B-2E19-4B1D-B116-2AAA34A2D16B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC45FE3B-2E19-4B1D-B116-2AAA34A2D16B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC45FE3B-2E19-4B1D-B116-2AAA34A2D16B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B0DFD41-AE30-4CF3-8758-6E5DDAE79A2E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B0DFD41-AE30-4CF3-8758-6E5DDAE79A2E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B0DFD41-AE30-4CF3-8758-6E5DDAE79A2E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B0DFD41-AE30-4CF3-8758-6E5DDAE79A2E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3CBC8DA6-CAD5-4351-B8D4-D803AB5191CD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3CBC8DA6-CAD5-4351-B8D4-D803AB5191CD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3CBC8DA6-CAD5-4351-B8D4-D803AB5191CD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3CBC8DA6-CAD5-4351-B8D4-D803AB5191CD}.Release|Any CPU.Build.0 = Release|Any CPU
		{5124FB45-0D83-4DA0-B142-8D71A8238C13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5124FB45-0D83-4DA0-B142-8D71A8238C13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5124FB45-0D83-4DA0-B142-8D71A8238C13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5124FB45-0D83-4DA0-B142-8D71A8238C13}.Release|Any CPU.Build.0 = Release|Any CPU
		{34CD5FCA-A16F-4873-923C-8C5B173F1F2C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34CD5FCA-A16F-4873-923C-8C5B173F1F2C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34CD5FCA-A16F-4873-923C-8C5B173F1F2C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34CD5FCA-A16F-4873-923C-8C5B173F1F2C}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF90B08B-4DBA-4A4D-9B25-D2750810DD12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF90B08B-4DBA-4A4D-9B25-D2750810DD12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF90B08B-4DBA-4A4D-9B25-D2750810DD12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF90B08B-4DBA-4A4D-9B25-D2750810DD12}.Release|Any CPU.Build.0 = Release|Any CPU
		{2860755F-CCBD-48C4-8E2D-1347D9C2EA37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2860755F-CCBD-48C4-8E2D-1347D9C2EA37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2860755F-CCBD-48C4-8E2D-1347D9C2EA37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2860755F-CCBD-48C4-8E2D-1347D9C2EA37}.Release|Any CPU.Build.0 = Release|Any CPU
		{129D9004-682B-4FFB-813D-6F3894557F12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{129D9004-682B-4FFB-813D-6F3894557F12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{129D9004-682B-4FFB-813D-6F3894557F12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{129D9004-682B-4FFB-813D-6F3894557F12}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EC52ED4D-D524-4599-A37F-287056CBDA90}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccProjectUniqueName0 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName0 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath0 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName1 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName1 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath1 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName2 = ../../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName3 = ../../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName4 = ../../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName5 = ../../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName6 = ../../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
		SccLocalPath7 = .
		SccProjectUniqueName8 = Core.Themis.API.Customers\\Core.Themis.API.Customers.csproj
		SccProjectName8 = Core.Themis.API.Customers
		SccLocalPath8 = Core.Themis.API.Customers
	EndGlobalSection
EndGlobal
