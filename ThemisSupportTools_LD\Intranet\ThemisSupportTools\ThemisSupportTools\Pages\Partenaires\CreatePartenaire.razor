﻿@page "/create-partenaire"
@using B<PERSON><PERSON><PERSON>ootstrap
@using ThemisSupportTools.Shared
@using Core.Themis.Libraries.DTO
@using Core.Themis.Libraries.Razor.Common.Components.Select
@using Core.Themis.Libraries.Razor.Common.Components.ModalDialog

@inject IConfiguration _configuration
@inject NavigationManager _navigationManager

@inject IPartnerManager PartenairesManager
@inject IWsAdminStructuresManager WsAdminStructuresManager

<EditForm Model="PartnerDTO" OnValidSubmit="@SavePartner">
    <DataAnnotationsValidator /> Paramètres
    <div class="form-group">
        <label>NOM:</label>
        <div>
            <InputText cssClass="form-control" @bind-Value="@PartnerDTO.PartnerName" />
            <ValidationMessage For="@(() => PartnerDTO.PartnerName)" />
        </div>
    </div>
    <div class="form-group" mb-3>
        <label>Secret Key:</label>
        <div>
            <InputText cssClass="form-control" @bind-Value="@PartnerDTO.SecretKey" />
            <span><button type="button" class="btn btn-secondary" @onclick:preventDefault="false" @onclick="GenerateSecretKey">Nouvelle secret key</button></span>
            <ValidationMessage For="@(() => PartnerDTO.SecretKey)" />
        </div>
    </div>

    <div class="form-group" mb-3>
        <label>Structures:</label>
        <div>
            <MultipleSelect SelectItemsLookup="SelectLookupStructures"></MultipleSelect>
        </div>
    </div>

    <div class="form-group">
        <label>Roles:</label>
        <div>
            <MultipleSelect SelectItemsLookup="SelectLookupRoles"></MultipleSelect>
        </div>
    </div>
    <div class="text-end">
        <button type="button" class="btn btn-secondary" @onclick="Cancel">Annuler</button>
        <button type="submit" class="btn btn-primary">Valider</button>
    </div>
</EditForm>

@code {
    [Parameter] public PartnerDTO PartnerDTO { get; set; } = new PartnerDTO();
    [Parameter] public List<StructureDTO> StructuresDTO { get; set; } = new List<StructureDTO>();
    [Parameter] public List<PartnerRoleDTO> PartnerRolesDTO { get; set; } = new List<PartnerRoleDTO>();
    //[Parameter] public EventCallback SavePartner { get; set; }

    private List<SelectLookup> SelectLookupStructures = new();
    private List<SelectLookup> SelectLookupRoles = new();

    public string PathBase { get; set; } = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        PathBase = _configuration["PathBase"]!;

        SelectLookupStructures = await WsAdminStructuresManager.GetSelectLookupStructuresAsync().ConfigureAwait(false);
        SelectLookupRoles = await WsAdminStructuresManager.GetSelectLookupRolesAsync().ConfigureAwait(false);

    }

    private void SavePartner()
    {

        if (!string.IsNullOrWhiteSpace(PartnerDTO.PartnerName) && !string.IsNullOrWhiteSpace(PartnerDTO.SecretKey))
        {
            //IEnumerable<int>? _innerRolesList = PartnerDTO.LstRolesOfPartner.Select(lrp => lrp.PartnerRoleId);
            var _innerRolesList = SelectLookupRoles.Where(slr => slr.IsSelected).Select(slr => slr.Value).ToList();


            //IEnumerable<int>? _innerStructuresList = PartnerDTO.LstStructuresLinked.Select(lst => lst.StructureCodeId);
            var _innerStructuresList = SelectLookupStructures.Where(slr => slr.IsSelected).Select(sls => sls.Value).ToList();


            //List<int> _liststructurespartenaires = PartnerDTO.LstStructuresLinked.Select(lst => lst.StructureId).ToList();

            List<int> _rolesList = PartnerDTO.LstRolesOfPartner.Select(r => r.PartnerRoleId).ToList();
            _rolesList = _innerRolesList ?? new();
            List<int> _structuresList = PartnerDTO.LstStructuresLinked.Select(s => int.Parse(s.StructureId)).ToList();
            _structuresList = _innerStructuresList ?? new();


            int partnerId = PartenairesManager.InsertPartner(PartnerDTO);





            //        PartenairesManager.UpdatePartenaireRoles(partnerId, SelectLookupRoles.Select(slr => slr.Value).ToList());
            //      PartenairesManager.UpdatePartenaireRoles(partnerId, SelectLookupStructures.Select(sls => sls.Value).ToList());


        }
    }
    private void GenerateSecretKey()
    {
        PartnerDTO.SecretKey = GenerateRandomPassword();
    }

    private string GenerateRandomPassword()
    {
        // Generate random password  as a secret key
        int length = 25;
        string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
        var random = new Random();
        string password = new string(Enumerable.Repeat(chars, length)
            .Select(s => s[random.Next(s.Length)]).ToArray());

        return password;
    }



    private void Cancel()
    {
        _navigationManager.NavigateTo("/partners");
    }
}