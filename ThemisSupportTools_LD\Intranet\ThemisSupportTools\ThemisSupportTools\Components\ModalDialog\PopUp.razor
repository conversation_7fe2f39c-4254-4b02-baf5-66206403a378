﻿@inject NavigationManager navigationManager;

@{
    var showClass = IsVisible ? "d-block" : "d-none";
}

<div class="modal @showClass" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true" style="display:block; background-color: rgba(10,10,10,.8);">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@HeaderText</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" @onclick="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>@BodyText</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" @onclick="Close">@CloseText</button>
            </div>
        </div>
    </div>
</div>




@code {

    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public EventCallback<bool> IsVisibleChanged { get; set; }

    [Parameter]
    public string? HeaderText { get; set; }

    [Parameter]
    public string? BodyText { get; set; }

    [Parameter]
    public string? CloseText { get; set; }


    public void Show(string bodyText, string closeButtonText, string headerText = "")
    {
        HeaderText = headerText;
        BodyText = bodyText;
        IsVisible = true;
        CloseText = closeButtonText;
        StateHasChanged();
    }

    private void Close()
    {
        if (BodyText.Contains("Aucune modification"))
        {
            HeaderText = string.Empty;
            BodyText = string.Empty;
            IsVisible = false;
        } 
        else
        {
            HeaderText = string.Empty;
            BodyText = string.Empty;
            IsVisible = false;
            navigationManager.NavigateTo($"");
            StateHasChanged();
        }      
    }
}
