{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    },
    "File": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "RodrigueFileLogger": {
      "Options": {
        "FolderPath": "D:\\LOGS\\Webservices\\API\\CORE_CUSTOMERS",
        "FilePath": "log_{structureid}_{date}.log"
      },
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Information"
      }
    }

  },
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "GlobalOpinionDB": "Server=************;Database=GLOBALOPINIONS;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true"
  },

  "WidgetCustomerUrl": "https://widgets.themisweb.fr/customers/v202/",
  "PartnerRodSK": "ser5#E6V6Z#Mp-7",
  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "PassBook": "PassBook"
  },
  "Cache": {
    //Cache pour la liste des adhésions en secondes
    "AdhesionsAbsoluteExpiration": 10,
    "AdhesionsSlidingExpiration": 2
  },
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\PROD\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "TypeRun": "PROD",
  "CryptoKey": "RodWebShop95",

  //"PathScriptSqlCommons": "\\\\**************\\webservices\\PROD\\libraries\\1.0.6\\[directory\\][filename][.structureid].sql",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",

  "WsAdminConnectionCache": 300,
  "CertificatPath": "\\\\**************\\api\\PROD\\CUSTOMERS\\1.0.0\\Certificats",
  "CertificatPassword": "Npsfq@99!@",
  "PassbookIconPath": "\\\\**************\\api\\PROD\\CUSTOMERS\\1.0.0\\",
  "PathImagesLogos": "\\\\*************\\customerfiles\\PROD\\[structureId]\\paiement\\images\\logosMaquettes\\",
  "PathPdfSkipQueue": "\\\\*************\\emails\\PROD\\[structureId]\\pdf\\",

  "PathPdfForViewMaquette": "\\\\**************\\sites\\PROD\\ReprintPDF\\",
  "PathPdf": "\\\\*************\\emails\\PROD\\[structureId]\\pdf\\",
  "PathEmails": "\\\\*************\\emails\\PROD\\[structureId]\\envoisok\\",

  "MTicket": {
    "Google": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\GoogleWallet\\Test\\mtickettest-391714-ff238abc11e4.json",
      "IssuerId": 3388000000022255685
    },
    "Apple": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\AppleWallet\\AppleCredentialParam.json"
    },
    "Samsung": {
      "AccountFile": "\\\\**************\\d\\CERTIFICATS\\SamsungWallet\\Test\\SamsungCredentialParam.json",
      "PartnerId": *******************,
      "CardId": "3gl7tatkio500"
    }
  }
}
  