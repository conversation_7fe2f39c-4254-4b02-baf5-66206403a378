﻿declare @myOrderId int = @pOrderId



DECLARE @SQL2 VARCHAR(4000),@SQL_MONTANT  VARCHAR(3000)
DECLARE @SQL VARCHAR(4000)
DECLARE  @DOSSIERID INT,@MANIFID INT,@FORMULEID INT, @SEANCEID INT,@NBENVOI INT,@PRODUITID INT
DECLARE @AUTREMONTANT DECIMAL(18,10)

--Set @LANGUAGEID=0
--set @COMMANDEID=318686
CREATE TABLE #T2 (dossier_id int,dossier_etat VARCHAR(2), entree_id INT, reference_unique_physique_id INT, type_tarif_id INT,
categorie_id INT, seance_id INT, manifestation_id INT, formule_id INT,montant DECIMAL(18,10),frais <PERSON>(18,10) )




-- FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 
DECLARE LISTDOS CURSOR SCROLL FOR 
	SELECT  cl.formule_id,clp.dossier_id, clp.manifestation_id,clp.seance_id, montant4+montant5+montant6+montant7+montant8+montant9 +montant10 as AutreMontant
	FROM commande_ligne_comp clp 
	INNER JOIN commande_ligne cl on cl.commande_ligne_id=clp.commande_ligne_id 
	WHERE  cl.type_ligne='DOS' and  clp.commande_id= @myOrderId;

OPEN LISTDOS; 
FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

WHILE @@FETCH_STATUS=0 
BEGIN 
	
		SET @SQL_MONTANT = 'esvg.montant1 + esvg.montant2 + case  when modecol4=''REMISE''  then - esvg.montant4 
			when modecol4=''TAXE'' or modecol4=''COMMISSION'' then  esvg.montant4
			else 0 END +
			case  when modecol5=''REMISE''  then - esvg.montant5
			when modecol5=''TAXE'' or modecol5=''COMMISSION'' then  esvg.montant5
			else 0 END +
			case  when modecol6=''REMISE''   then - esvg.montant6
			when modecol6=''TAXE'' or modecol6=''COMMISSION'' then  esvg.montant6
			else 0 END +
			case  when modecol7=''REMISE''  then - esvg.montant7
			when modecol7=''TAXE'' or modecol7=''COMMISSION'' then  esvg.montant7
			else 0 END +
			case  when modecol8=''REMISE''  then - esvg.montant8
			when modecol8=''TAXE'' or modecol8=''COMMISSION'' then  esvg.montant8
			else 0 END +
			case  when modecol9=''REMISE''  then - esvg.montant9
			when modecol9=''TAXE''or modecol9=''COMMISSION'' then  esvg.montant9
			else 0 END +
			case  when modecol10=''REMISE''  then - esvg.montant10
			when modecol10=''TAXE'' or modecol10=''COMMISSION'' then  esvg.montant10
			else 0 END as montant, esvg.montant2 as frais ';

	SET @SQL = 'INSERT INTO #T2 
		SELECT esvg.dossier_id, d.dossier_etat, esvg.entree_id, e.reference_unique_physique_id,esvg.type_tarif_id, esvg.categorie_id, esvg.seance_id,  '
		+LTRIM(STR(@MANIFID)) +' as manifestation_id ,'+LTRIM(STR(@FORMULEID)) +' as formule_id,'

		+ @SQL_MONTANT + 

		' FROM structure, entreesvg_'+LTRIM(STR(@MANIFID)) +' esvg 
		inner join entree_'+LTRIM(STR(@MANIFID)) +' e on e.entree_id = esvg.entree_id and e.seance_id = esvg.seance_id
		INNER JOIN dossier_'+LTRIM(STR(@MANIFID)) + ' d on d.dossier_id=esvg.dossier_id
		WHERE esvg.dossier_id =' + LTRIM(STR(@DOSSIERID)) + '
		and esvg.seance_id = ' + LTRIM(STR(@SEANCEID)) + '
		and esvg.dossier_v in (select MAX(dossier_v) from entreesvg_190 esvg2 where esvg2.dossier_id = esvg.dossier_id and esvg2.entree_id = esvg.entree_id and esvg.seance_id = esvg2.seance_id)'

	print(@sql)
	
	EXEC(@SQL )	
PRINT @SQL;
FETCH NEXT FROM LISTDOS INTO @FORMULEID,@DOSSIERID,@MANIFID,@SEANCEID,@AUTREMONTANT; 

END

CLOSE LISTDOS; 
DEALLOCATE LISTDOS; 

SELECT dossier_id, dossier_etat, #T2.formule_id ,form_abon_nom as formule_nom,l.lieu_id , l.lieu_nom ,
	m.manifestation_id , manifestation_nom ,
	convert(int,s.seance_id) as seance_id,	
	seance_date_deb,entree_id , 
	pos_x, pos_y, siege , rang , 
	c.categ_id ,categ_nom ,z.zone_id ,z.zone_nom ,
	sec.section_id , sec.section_nom ,et.etage_id , 
	etage_nom , rlp.denomination_id ,denom_nom ,
	tt.type_tarif_id  ,type_tarif_nom ,montant, frais ,
	 tva.tva_taux, tva_libelle as tva_name,
	 
	CASE WHEN NEPASAFFICHERDATE like 'O' THEN cast(0 as bit) ELSE cast(1 as bit) END as ShowSessionDate, 		
    CASE WHEN NEPASAFFICHERDATE like 'H' THEN cast(0 as bit) WHEN NEPASAFFICHERDATE like 'O' THEN cast(0 as bit) ELSE cast(1 as bit) END as ShowSessionHour
	 
	 
--	 	 CAST(1 AS BIT) as ShowSessionDate,
--	CAST(1 AS BIT) as ShowSessionHour
	
	FROM #T2 inner join categorie c on c.categ_id=#T2.categorie_id
	inner join type_tarif tt on tt.type_tarif_id=#T2.type_tarif_id
	inner join seance s on s.seance_id=#T2.seance_id
	inner join tva on s.taux_tva1_id = tva.tva_id
	inner join manifestation m on m.manifestation_id=#T2.manifestation_id
	inner join reference_lieu_physique rlp on rlp.ref_uniq_phy_id=#T2.reference_unique_physique_id
	left outer join formule_abonnement fa on fa.form_abon_id=#T2.formule_id
	inner join zone z on z.zone_id=rlp.zone_id
	inner join section sec on sec.section_id=rlp.section_id
	inner join denomination de on de.denom_id=rlp.denomination_id
	inner join etage et on et.etage_id = rlp.etage_id
	inner join lieu l on l.lieu_id = rlp.lieu_id

PRINT @SQL2;
EXEC(@SQL2);
--PRINT @SQLLISTFRAIS;

DROP TABLE #T2