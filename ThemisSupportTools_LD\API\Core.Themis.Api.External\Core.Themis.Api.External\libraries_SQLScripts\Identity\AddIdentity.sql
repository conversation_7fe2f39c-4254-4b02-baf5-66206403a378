﻿/*
declare @checkEmailUnicity bit
set @checkEmailUnicity = 0

-- correspond à EMAIL et TYPE_EMAIL dans le config.ini 
declare @pPostalTelEmail int
set @pPostalTelEmail= 6

declare @plibelleEmail int
set @plibelleEmail= 5120005

-- correspond à TELEPHONE et TYPE_TELEPHONE dans le config.ini 
declare @pPostalTelPhone int
set @pPostalTelPhone= 1

declare @plibellePhone int
set @plibellePhone= 5120001

-- correspond à FAX et TYPE_FAX dans le config.ini 
declare @pPostalTelFax int
set @pPostalTelFax = 2

declare @plibelleFax int
set @plibelleFax= 5120004

-- correspond à PORTABLE et TYPE_PORTABLE dans le config.ini 
declare @pPostalTelMobilePhone int
set @pPostalTelMobilePhone= 3

declare @plibelleMobilePhone int
set @plibelleMobilePhone= 5120003


declare @pemail varchar(50)
set @pemail ='<EMAIL>'

declare @pFax varchar(50)
set @pFax =''

declare @pPhoneNumber varchar(50)
set @pPhoneNumber =''

declare @pMobilePhoneNumber varchar(50)
set @pMobilePhoneNumber =''


declare @pIdentityComplement varchar(300)=''
declare @pNom varchar(300) = 'assoufi'
declare @pPrenom varchar(300) ='rachida'
declare @pPassword varchar(max) ='rachida'

declare @pAppellationId int = 2
declare @pDateOfBirthday varchar(500) = '01/01/1900'
declare @pAddress1 varchar(300)=''
declare @pAddress2 varchar(300)=''
declare @pAddress3 varchar(300)=''
declare @pAddress4 varchar(300)=''
declare @pPostalCode varchar(10)=''
declare @pVille varchar(300)=''
declare @pPays varchar(300)=''
declare @pOperateurId int=5650002
declare @pFiliereid int=9910006


*/


/* Variables locales SQL */
declare @postalTel1 varchar(300)=''
declare @postalTelLib1 int=0
declare @postalTel2 varchar(300)=''
declare @postalTelLib2 int=0
declare @postalTel3 varchar(300)=''
declare @postalTelLib3 int=0
declare @postalTel4 varchar(300)=''
declare @postalTelLib4 int=0
declare @postalTel5 varchar(300)=''
declare @postalTelLib5 int=0
declare @postalTel6 varchar(300)=''
declare @postalTelLib6 int=0
declare @postalTel7 varchar(300)=''
declare @postalTelLib7 int=0

Declare @TblTemp table (Email varchar(250),Identite_id int)

if @checkEmailUnicity = 1 
begin
	insert into @TblTemp
	select top 1 EMail,Identite_id from (
	select 
	case when @pPostalTelEmail =1 then postal_tel1 else '' end +
	case when @pPostalTelEmail =2 then postal_tel2 else '' end +
	case when @pPostalTelEmail =3 then postal_tel3 else '' end +
	case when @pPostalTelEmail =4 then postal_tel4 else '' end +
	case when @pPostalTelEmail =5 then postal_tel5 else '' end +
	case when @pPostalTelEmail =6 then postal_tel6 else '' end +
	case when @pPostalTelEmail =7 then postal_tel7 else '' end
	as email, *
	from identite
	) s
	where email =@pEmail 
	--and identite_password = ''
	and FicheSupprimer = 'N'
	order by identite_id desc

end


Declare @CountResult int

set @CountResult = (select count(*) from @TblTemp)

if @CountResult >0 
	begin
		--identite existe déjà
		select -1  as nextIdentityId
	
	end

if @CountResult =0 
	begin

	
		if @pPostalTelEmail = 1
		begin
			set @PostalTel1 = @pemail
			set @postalTelLib1 = @plibelleEmail
		end 
		if @pPostalTelEmail = 2
		begin
			set @PostalTel2 = @pemail
			set @postalTelLib2 = @plibelleEmail
		end 
		if @pPostalTelEmail = 3
		begin
			set @PostalTel3 = @pemail
			set @postalTelLib3 = @plibelleEmail
		end 
		if @pPostalTelEmail = 4 
		begin
			set @PostalTel4 = @pemail
			set @postalTelLib4 = @plibelleEmail
		end 
		if @pPostalTelEmail = 5 
		begin
			set @PostalTel5 = @pemail
			set @postalTelLib5 = @plibelleEmail
		end 
		if @pPostalTelEmail = 6 
		begin
			set @PostalTel6 = @pemail
			set @postalTelLib6 = @plibelleEmail
		end 
		if @pPostalTelEmail = 7
		begin
			set @PostalTel7 = @pemail
			set @postalTelLib7 = @plibelleEmail
		end 

		if @pPostalTelFax = 1
		begin
			set @PostalTel1 = @pFax
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelFax = 2
		begin
			set @PostalTel2 = @pFax
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelFax = 3
		begin
			set @PostalTel3 = @pFax
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelFax = 4
		begin
			set @PostalTel4 = @pFax
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelFax = 5
		begin
			set @PostalTel5 = @pFax
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelFax = 6
		begin
			set @PostalTel6 = @pFax
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelFax = 7
		begin
			set @PostalTel7 = @pFax
			set @postalTelLib7 = @plibelleFax
		end 

		if @pPostalTelPhone = 1
		begin
			set @PostalTel1 = @pPhoneNumber
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelPhone = 2
		begin
			set @PostalTel2 = @pPhoneNumber
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelPhone = 3
		begin
			set @PostalTel3 = @pPhoneNumber
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelPhone = 4
		begin
			set @PostalTel4 = @pPhoneNumber
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelPhone = 5
		begin
			set @PostalTel5 = @pPhoneNumber
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelPhone = 6
		begin
			set @PostalTel6 = @pPhoneNumber
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelPhone = 7
		begin
			set @PostalTel7 = @pPhoneNumber
			set @postalTelLib7 = @plibelleFax
		end 

		if @pPostalTelMobilePhone = 1
		begin
			set @PostalTel1 = @pMobilePhoneNumber
			set @postalTelLib1 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 2
		begin
			set @PostalTel2 = @pMobilePhoneNumber
			set @postalTelLib2 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 3
		begin
			set @PostalTel3 = @pMobilePhoneNumber
			set @postalTelLib3 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 4
		begin
			set @PostalTel4 = @pMobilePhoneNumber
			set @postalTelLib4 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 5
		begin
			set @PostalTel5 = @pMobilePhoneNumber
			set @postalTelLib5 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 6
		begin
			set @PostalTel6 = @pMobilePhoneNumber
			set @postalTelLib6 = @plibelleFax
		end 
		if @pPostalTelMobilePhone = 7
		begin
			set @PostalTel7 = @pMobilePhoneNumber
			set @postalTelLib7 = @plibelleFax
		end 
	end



if @CountResult =0 
	begin
		-- Select 'Client Non Trouvé'
	
			declare @nextIdentityId int
						
			UPDATE cpt_client SET compteur = compteur +1, @nextIdentityId=compteur+1; 
			SELECT @nextIdentityId  as nextIdentityId;


			INSERT INTO identite (identite_id,identite_nom,identite_complement,identite_titre_id,appellation_id,identite_date_naissance,postal_rue1
			,postal_rue2,postal_rue3,postal_rue4,postal_cp,postal_ville,postal_region,postal_pays,postal_tel1,postal_tel1_libelle_id,postal_tel2,postal_tel2_libelle_id
			,postal_tel3,postal_tel3_libelle_id,postal_tel4,postal_tel4_libelle_id,postal_tel5,postal_tel5_libelle_id,facture_rue1,facture_rue2
			,facture_rue3,facture_rue4,facture_cp,facture_ville,facture_region,facture_pays,facture_tel1,facture_tel1_libelle_id,facture_tel2,facture_tel2_libelle_id
			,facture_tel3,factue_tel3_libelle_id,facture_tel4,facture_tel4_libelle_id,marqueur_mailing_id,identite_validite_debut,identite_validite_fin
			,identite_v,operateur_id,ref_compta,ref_perso,etiquette1,etiquette2,etiquette3,etiquette4,etiquette5,identite_libre1,identite_libre2,identite_libre3
			,identite_libre4,identite_libre5,postal_tel6,postal_tel6_libelle_id,postal_tel7,postal_tel7_libelle_id,identite_prenom,filiere_id,identite_remise
			,identite_remisedg,montant_credit,montant_debit,FicheSupprimer,statut_financier,appelinterloc_id,
			--identite_groupe_id,,
			IDENTITE_DATE_CREATION,
			IDENTITE_DATE_MODIFICATION,
			--operateurmodif_id,
			facture_tel5,facture_tel5_libelle_id,facture_tel6,facture_tel6_libelle_id,facture_tel7
			,facture_tel7_libelle_id,identite_password)
				 VALUES
			 (@nextIdentityId, @pNom, @pIdentityComplement, 0, @pAppellationId, @pDateOfBirthday, @pAddress1,@pAddress2,@pAddress3,@pAddress4, @pPostalCode, @pVille, '',
			  @pPays,@postalTel1,@postalTelLib1,@postalTel2,@postalTelLib2, @postalTel3,@postalTelLib3, @postalTel4,@postalTelLib4,
			  @postalTel5,@postalTelLib5,'','','','','','', '','','','','','','','',
			 '','',0,'01/01/1900','01/01/1900',0,@pOperateurId,'', '','','','','','','','','','','',
			 @postalTel6,@postalTelLib6, @postalTel7,@postalTelLib7,@pPrenom,@pFiliereid,0,0,0,0,
			 'N',0,0,
			 --[GROUPE_ID], valeur par defaut 0
			 GETDATE(),GETDATE(),
			 --[OPERATEUR_MODIF_ID],  valeur par defaut 0
			 '',0,'',0,'',0,@pPassword)

	end


