﻿
<div class="col-12 mb-3">
    <div class="col-auto mb-2 mb-md-0">
        <div class="input-group">
            <input class="form-control border-right-0" type="text" id="filter_search"
            @bind="@searchTerm" @bind:event="oninput" @onkeyup="@Search"
                   placeholder="Rechercher" />

            <div class="input-group-append">
                <div class="input-group-text bg-transparent"><i class="bi bi-search"></i></div>
            </div>
        </div>
    </div>
</div>

@code {
   
    [Parameter]
    public EventCallback<string> OnSearch { get; set; }

    private string searchTerm = string.Empty;

    private async Task Search()
    {
        await OnSearch.InvokeAsync(searchTerm);
    }
}
