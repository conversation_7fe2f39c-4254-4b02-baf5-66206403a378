﻿@page "/encrypt-password"
@using System.ComponentModel.DataAnnotations

<h3>🔐 Chiffreur de mot de passe</h3>

<EditForm Model="@model" OnValidSubmit="@EncryptPassword1">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="mb-3">
        <label>Mot de passe à chiffrer :</label>
        <InputText @bind-Value="model.PlainPassword" class="form-control" />
    </div>

    <button type="submit" class="btn btn-primary">Chiffrer</button>
</EditForm>

@if (!string.IsNullOrEmpty(encryptedPassword))
{
    <div class="alert alert-success mt-3">
        <strong>Mot de passe chiffré :</strong><br />
        <code>@encryptedPassword</code>
    </div>
}

@code {
    private PasswordModel model = new();
    private string encryptedPassword;

    private void EncryptPassword1()
    {
        encryptedPassword = CryptoHelper.Encrypt(model.PlainPassword);
    }

    public class PasswordModel
    {
        [Required]
        public string PlainPassword { get; set; }
    }
}
