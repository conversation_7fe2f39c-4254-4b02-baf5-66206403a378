﻿using AutoMapper;
using Core.Themis.Libraries.BLL;

using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Org.BouncyCastle.Ocsp;
using System.Threading.Tasks;
using Seat = Core.Themis.Libraries.DTO.exposedObjects.Seat;

namespace Core.Themis.Api.External.Controllers
{

    /// <summary>
    /// 10. basket
    /// </summary>
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class BasketController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly ISeatManager _seatManager;
        private readonly IWebUserManager _webUserManager;
        private readonly IEventManager _eventManager;
        private readonly IGestionPlaceManager _gestionPlaceManager;
        private readonly IBasketManager _basketManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IGestionTraceManager _gestionTrace;


        //private readonly IApiUtilities _apiUtilities;

        private IMapper Mapper
        {
            get;
        }
        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        public BasketController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IGestionTraceManager gestionTraceManager,
            IMapper mapper,
            ISeatManager seatManager,
            IWebUserManager webUserManager,
            IEventManager eventManager,
            IGestionPlaceManager gestionPlaceManager,
            IBasketManager basketManager,
            IBuyerProfilManager buyerProfilManager
            )
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _gestionTrace = gestionTraceManager;
            _webUserManager = webUserManager;
            _seatManager = seatManager;
            _eventManager = eventManager;
            _gestionPlaceManager = gestionPlaceManager;
            this.Mapper = mapper;
            _basketManager = basketManager;
            _buyerProfilManager = buyerProfilManager;
        }

        /// <summary>
        /// Get Basket
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>  
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="basketId">The basket identifier of the internet user</param>
        /// <returns>Basket</returns>
        /// <remarks>Returns informations of the internet user basket</remarks>

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Basket))]
        [HttpGet]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Basket/{langCode}/{basketId}")]
        public IActionResult Get(int structureId, string langCode, int basketId)
        {

            BasketDTO baskToRet = _basketManager.GetBasketById(structureId, basketId);

            //BasketDTO baskToRet = _basketManager.Get(sqlConnWT, structureId, bask.BasketId, 0, langCode, "basketcontroller.getbasketfromopen").FirstOrDefault();

            //sqlConnWT.Dispose();

            _basketManager.FillFromOpen(structureId, baskToRet, langCode);

            // sqlConnOpen.Dispose();

            Basket baskE = (Basket)Mapper.Map(baskToRet, typeof(BasketDTO), typeof(Basket));

            //_gestionTrace.WriteLogMessage(structureId, and.WebUserId, $"AddSeats({structureId}) ok, basket {baskE.Id}", TypeLog.LogGenerique);

            Logger.Debug(structureId, $"AddSeats({structureId}) ok, basket {baskE.Id}");
            return Ok(baskE);
        }

        /// <summary>
        /// Add seats to basket of the internet user
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="buyerProfilId"> The buyer profil identifier</param>
        /// <param name="bpLogin">The buyer profil login</param>
        /// <param name="bpPassword">The buyer profil password</param>
        /// <param name="flagDemand"></param>
        /// <returns>The basket</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Basket))]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/AddBasket/{langCode}/{bpLogin}/{bpPassword}")]
        public IActionResult AddSeats(int structureId, string langCode, int buyerProfilId, string bpLogin, string bpPassword, [FromBody] FlagDemand flagDemand)
        {
            Logger.Debug(structureId, $"AddSeats({structureId}), {JsonConvert.SerializeObject(flagDemand)}...");
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            // var apiOfferUrl = _configuration["ApiOfferUrl"].ToString();

            try
            {
                _gestionTrace.WriteLogMessage(structureId, flagDemand.WebUserId, $"AddBasket({structureId} :{JsonConvert.SerializeObject(flagDemand)})...", TypeLog.LogGenerique);
                int myBprofilId = 0;
                //BuyerProfilDTO buyerProfil = BuyerProfilManager.Get(structureId, buyerProfilId, bpLogin, bpPassword);

                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByLoginPassword(structureId, bpLogin, bpPassword);
                if (buyerProfil != null)
                {
                    myBprofilId = buyerProfil.Id;
                }

                BasketDTO bask = _basketManager.CreateBasketIfNotExists(structureId, flagDemand.WebUserId, 0, "C", null);
                bask.Hash = bask.GetHash();

                foreach (SessionCategDemand flg in flagDemand.ListDemands)
                {
                    SessionCategDemand ruleResponse = new SessionCategDemand()
                    {
                        SessionId = flg.SessionId,
                        ZoneId = flg.ZoneId,
                        FloorId = flg.FloorId,
                        SectionId = flg.SectionId,
                        CategoryId = flg.CategoryId,
                        ListRules = new List<rule>()
                    };
                   
                    Libraries.DTO.WTObjects.WebUser webUser = _webUserManager.Get(structureId, flagDemand.WebUserId);

                    var listGpAsked = flg.ListRules.Select(r => r.RuleId).Distinct().ToList();

                    List<EventDTO> listEvent =
                        listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, webUser.IdentiteId,
                         0, flg.SessionId, myBprofilId, "", listGpAsked);

                    if (listEvent == null || listEvent.Count == 0)
                    {
                        Logger.Error(structureId, $"AddBasket({structureId}), can't retrieve session {flg.SessionId}");
                        return Problem($"can't retrieve session {flg.SessionId}", null, StatusCodes.Status404NotFound);
                        //return Problem($"priceGrid for session {flg.SessionId}", null, 401, "priceGridError");
                    }

                    var zones = listEvent[0].ListSessions[0].ListZones;
                    var floors = zones.SelectMany(f => f.ListFloors).ToList();
                    var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                    var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                    var gpList = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Distinct().ToList();


                    List<int> listGpsId = new List<int>();
                    int coeff = 0;
                    foreach (rule r in flg.ListRules)
                    {
                        int ruleId = r.RuleId;

                        if (r.RuleId != 0)
                        {
                            List<int> listTheseSeatsId = r.ListSeats.Select(s => s.SeatId).ToList();
                            //List<SeatDTO> listThesesSeats = SeatManager.Get(sqlConnOpen, structureId, listEvent[0].EventId, listTheseSeatsId, langCode, pathScriptSqlCommons);
                            List<SeatDTO> listThesesSeats = _seatManager.Get(structureId, listEvent[0].EventId, listTheseSeatsId, langCode);

                            var thisGpS = _gestionPlaceManager.Load(structureId, new List<int> { r.RuleId });
                            if (thisGpS == null || thisGpS.Count() == 0)
                            {

                                Logger.Error(structureId, $"AddBasket({structureId}) pour webuser {flagDemand.WebUserId}, can't retrieve rules {r.RuleId}");
                                return Problem($"can't retrieve rules {r.RuleId}", null, StatusCodes.Status404NotFound);
                            }

                            //var seatsInDB = _seatManager.Get(structureId, listEvent[0].EventId, r.ListSeats.Select(s => s.SeatId).ToList(), langCode);
                            var thisGp = thisGpS.FirstOrDefault();

                            foreach (var seat in r.ListSeats)
                            {

                                SeatDTO thisSeat = listThesesSeats.Where(s => s.SeatId == seat.SeatId).FirstOrDefault();
                             
                                if (thisSeat.State != 'L')
                                {
                                    Exception ex = new Exception($"seat {thisSeat.SeatId}, state is {thisSeat.State} !");
                                    throw ex;
                                }

                                if (thisSeat.SessionId != thisGp.SessionId || thisSeat.CategoryId != thisGp.CategoryId)
                                {
                                    Exception ex = new Exception($"seat {thisSeat.SeatId}, rule id do not match seat infos sess {thisSeat.SessionId}!={thisGp.SessionId} OR ctg {thisSeat.CategoryId}!={thisGp.CategoryId} !");
                                    throw ex;
                                }

                                _basketManager.AddSeatInBasketLine(structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], thisSeat, thisGp, 0);

                            }
                            coeff += r.SeatsCount;
                            listGpsId.Add(r.RuleId);
                        }
                    }
                }

                BasketDTO baskToRet = _basketManager.GetBasketById(structureId, bask.BasketId);

                //BasketDTO baskToRet = _basketManager.Get(sqlConnWT, structureId, bask.BasketId, 0, langCode, "basketcontroller.getbasketfromopen").FirstOrDefault();

                //sqlConnWT.Dispose();

                _basketManager.FillFromOpen(structureId, baskToRet, langCode);

                // sqlConnOpen.Dispose();

                Basket baskE = (Basket)Mapper.Map(baskToRet, typeof(BasketDTO), typeof(Basket));

                _gestionTrace.WriteLogMessage(structureId, flagDemand.WebUserId, $"AddSeats({structureId}) ok, basket {baskE.Id}", TypeLog.LogGenerique);

                Logger.Debug(structureId, $"AddSeats({structureId}) ok, basket {baskE.Id}");

                return Ok(baskE);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"AddSeats({structureId}) pour webuser {flagDemand.WebUserId}, " +
                    $"listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)} {ex.Message} {ex.StackTrace}");

                _gestionTrace.WriteLogErrorMessage(structureId, flagDemand.WebUserId, $"AddSeats({structureId}) ko, basket {ex.Message} {ex.StackTrace}");

                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }





        /// <summary>
        /// Flag seats automatic according to the demand
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="flagDemand"></param>
        /// <returns>Seats</returns>
        /// <remarks>
        /// Example of request:
        /// 
        ///     flag 2 seats with rule 789 on session 567, category 9940001 for user 123
        ///     
        ///     POST api/994/SeatsFlag/auto
        ///     {
        ///       "webUserId": 123,
        ///       "listDemands": [
        ///         {
        ///           "sessionId": 567,
        ///           "categoryId": 9940001,
        ///           "listRules": [
        ///             {
        ///               "ruleId": 789,
        ///               "seatsCount": 2
        ///             }
        ///           ]
        ///         }
        ///       ]
        ///     }   
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FlagResponse))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/SeatsFlag/auto")]

        public IActionResult FlagAuto(int structureId, [FromBody] FlagDemand flagDemand)
        {
            Logger.Debug(structureId, $"FlagAuto({structureId}) pour webuser {flagDemand.WebUserId}, listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)}");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            try
            {
                var flagResponse = _seatManager.FlagAuto(structureId, flagDemand);

                Logger.Debug(structureId, $"flagResponse={JsonConvert.SerializeObject(flagResponse)}");
                return Ok(flagResponse);

            }
            catch (Exception ex)
            {

                Logger.Error(structureId, $"FlagAuto({structureId}) pour webuser {flagDemand.WebUserId}, " +
                    $"listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)} {ex.Message} {ex.StackTrace}");

                _gestionTrace.WriteLogErrorMessage(structureId, flagDemand.WebUserId, $"FlagAuto: {ex.Message} {ex.StackTrace}");

                // "Flag(" + structureId + ") pour webuser " + flagDemand.WebUserId + ", listDemands.Count=" + flagDemand.SessionCategDemand.Count + " :" + JsonConvert.SerializeObject(flagDemand));
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Unflag seats
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="unflagDemand">Object wich contains the seats id list to unflag</param>
        /// <returns>True/False</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpDelete]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/SeatsFlag")]

        public IActionResult UnFlag(int structureId, [FromBody] UnFlagDemand unflagDemand)
        {


            Logger.Debug(structureId, $"UnFlag({structureId} :{JsonConvert.SerializeObject(unflagDemand)})...");
            _gestionTrace.WriteLogMessage(structureId, unflagDemand.WebUserId, $"UnFlag({structureId} :{JsonConvert.SerializeObject(unflagDemand)})...", TypeLog.LogGenerique);

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            try
            {

                bool result = true;
                foreach (var session in unflagDemand.ListSessions)
                {
                    List<int> ltounflag = new List<int>();
                    foreach (var s in session.ListSeats)
                    {
                        ltounflag.Add(s.SeatId);
                    }

                    result = _seatManager.UnFlag(structureId, 0, session.SessionId, unflagDemand.WebUserId, ltounflag, "");

                    var myBasket = _basketManager.GetBasketByWebUserIdAndBasketState(structureId, unflagDemand.WebUserId, "C");

                    if (myBasket != null && myBasket.BasketId > 0)
                    {
                        _basketManager.DeleteSeatsOfSessionBasket(structureId, session.SessionId, myBasket.BasketId, ltounflag);

                    }

                }
                return Ok(result);
            }

            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"UnFlag({structureId}) {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, unflagDemand.WebUserId, $"UnFlag: {ex.Message} {ex.StackTrace}");

                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Link (=flag) the seats to the webUserId: reserved for 20 minutes
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <param name="webUserId">The web user identifier</param>
        /// <param name="seatsIds"></param>
        /// <returns>The linked (flagged) places of the webUserId on this session</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Seat>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/SeatsFlag/plan/{eventId}/{sessionId}/{webUserId}")]

        public IActionResult FlagManually(int structureId, int eventId, int sessionId, int webUserId, [FromBody] List<int> seatsIds)
        {

            try
            {
                Logger.Info(structureId, $"FlagManually({structureId}, {eventId}, {sessionId}, {webUserId}, seatsIds=[{string.Join(",", seatsIds)}])...");
                _gestionTrace.WriteLogMessage(structureId, webUserId, $"({structureId}, {eventId}, {sessionId}, {webUserId}, seatsIds=[{string.Join(",", seatsIds)}])...", TypeLog.LogGenerique);
                //string typeRun = _configuration["TypeRun"].ToString();
                //var pathScriptSqlCommons = _configuration["PathScriptSqlCommons"].ToString();
                //var wsAdminConnectionString = _configuration.GetConnectionString("WsAdminDB");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                {
                    Logger.Error(structureId, $"structureId {structureId} is not allowed");
                    var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                    return pb;
                }

                var webU = _webUserManager.Get(structureId, webUserId);
                //var webUE = _webUserRepository.GetById(structureId, webUserId);

                int paId = webU.ProfilAcheteurId;
                int identiteId = webU.IdentiteId;


                var result = _seatManager.FlagManually(structureId, eventId, sessionId, webUserId, seatsIds.ToList(), "T" + webUserId, paId, identiteId);
                List<Seat> seats = (List<Seat>)Mapper.Map(result, typeof(List<SeatDTO>), destinationType: typeof(List<Seat>));

                //var result = SeatManager.FlagManually(structureId, sqlConnOpen, pathScriptSqlCommons, eventId, sessionId, webUserId, seatsIds.ToList(), "T" + webUserId);
                Logger.Debug(structureId, $"FlagManually({structureId}, {eventId}, {sessionId}, {webUserId}, {JsonConvert.SerializeObject(seats)})");

                if (seats.Count == seatsIds.Count)
                {
                    return Ok(seats);
                }
                else
                {
                    var pb = Problem($"can't flag ({seatsIds.Count} in, {seats.Count} flags)", null, StatusCodes.Status400BadRequest);
                    return pb;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"FlagManually({structureId},{eventId},{sessionId},[{string.Join(";", seatsIds)}]) error {ex.Message} {ex.StackTrace}");
                _gestionTrace.WriteLogErrorMessage(structureId, webUserId, $"FlagManually({structureId}) ko, basket {ex.Message} {ex.StackTrace}");
                var pb = Problem(ex.Message, null, StatusCodes.Status500InternalServerError);

                return pb;
            }
        }
    }
}
