using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Microsoft.AspNetCore.Authentication.Cookies;
using NLog.Web;
using NLog;
using Microsoft.AspNetCore.StaticFiles;

var logger = NLog.LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
logger.Debug("init main");

try
{
    var builder = WebApplication.CreateBuilder(args);

 

    // NLog: Setup NLog for Dependency injection
    builder.Logging.ClearProviders();
    builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
    builder.Host.UseNLog();

    // Add services to the container.
    builder.Services.AddRazorPages();
    builder.Services.AddServerSideBlazor();
    builder.Services.AddBlazorBootstrap();

    // Rodrigue injection
    builder.Services.AddRodrigueDataServices();
    builder.Services.AddRodrigueManager();
    builder.Services.AddRodrigueMapper();
    
    // In-Memory Caching
    builder.Services.AddMemoryCache();
    builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

    //Ldap Authentification

    builder.Services.AddControllers();


    builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
              .AddCookie(options =>
              {
                  options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
                  options.LoginPath = "/Account/loginactivedirectory";
                  options.AccessDeniedPath = "/Account/AccessDenied";
              });

    var configuration = builder.Configuration;

    var app = builder.Build();

    app.UseAuthentication();
    app.UseAuthorization();




    builder.WebHost.UseWebRoot("wwwroot"); builder.WebHost.UseStaticWebAssets();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }
    else { 
        app.UseExceptionHandler("/Error");
        // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
        app.UseHsts();
    }

    app.UseHttpsRedirection();
    app.UseStaticFiles();

    app.UseRouting();

    app.Use((context, next) =>
    {
        if (context.Request.Host.ToString().Contains("localhost"))
        {
            context.Request.PathBase = "/";
        }
        else
        {
            context.Request.PathBase = configuration.GetValue<string>("PathBase");
        }

        return next();
    });


    NLog.LogManager.Configuration.Variables.Add("environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
    string version = Directory.GetParent(AppContext.BaseDirectory).Name;
    NLog.LogManager.Configuration.Variables.Add("version", version);

    FileExtensionContentTypeProvider contentTypeProvider = new FileExtensionContentTypeProvider();
    if (!contentTypeProvider.Mappings.ContainsKey(".less"))
    {
        contentTypeProvider.Mappings.Add(".less", "text/css");
    }







    //Ldap Authentification



    app.MapBlazorHub();
    app.MapControllers();
    app.MapFallbackToPage("/_Host");




    app.Run();
}
catch (Exception exception)
{
    logger.Error(exception, "Stopped program because of exception");
    throw;
}
finally
{
    // Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
    NLog.LogManager.Shutdown();
}





