﻿@using System.Net.Http
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using ThemisSupportTools
@using ThemisSupportTools.Shared
@using System.DirectoryServices
@using System.IO
@using Core.Themis.Libraries.DTO.TST
@using Core.Themis.Libraries.DTO.ThemisSupportTools.ConfigIniModels
@using Core.Themis.Libraries.BLL.Managers.TST.Interfaces
@using Core.Themis.Libraries.DTO.Enums.ThemisSupportTools
@using System.Xml.Linq;
@using Core.Themis.Libraries.Razor.Common.Components.ModalDialog
@using Core.Themis.Libraries.Razor.Common.Components.TypesComponents
@using Core.Themis.Libraries.DTO.Lookup