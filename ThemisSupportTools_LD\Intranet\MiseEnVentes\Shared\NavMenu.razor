﻿<header>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <li class="nav-item1">
            <NavLink class="nav-link" href="./">
                <span class="oi oi-eye" aria-hidden="true"></span>
                <span>Prochaines MEV</span>
            </NavLink>
        </li>


        <div class="container-fluid">
            <div class="@NavMenuCssClass navbar-collapse">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0 align-items-center">
                    <li class="nav-item">
                        <NavLink class="nav-link" href="admin">
                            <span class="oi oi-home" aria-hidden="true"></span> MISES EN VENTES
                        </NavLink>
                    </li>
                    <li class="nav-item">
                        <NavLink class="nav-link" href="intervenants">
                            <span class="oi oi-plus" aria-hidden="true"></span> INTERVENANTS
                        </NavLink>
                    </li>
                    <li class="nav-item">
                        <NavLink class="nav-link" href="Plateformes">
                            <span class="oi oi-list-rich" aria-hidden="true"></span> PLATEFORMES
                        </NavLink>
                    </li>
                    <!--<li class="nav-item">
                    <NavLink class="nav-link" href="encrypt-password">
                        <span class="oi oi-list-rich" aria-hidden="true"></span> Crypt
                    </NavLink>
                </li>-->
                </ul>
            </div>
        </div>
    </nav>
</header>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    //private string url = @Request.PathBase.Value;

            private void ToggleNavMenu()
            {
                collapseNavMenu = !collapseNavMenu;
            }
}
