using System;
using MySql.Data.MySqlClient;
using MySql.EntityFrameworkCore;
using BDD;
using Cri.Services;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using QuestPDF.Infrastructure;
using System.Configuration;

QuestPDF.Settings.License = LicenseType.Community;

var builder = WebApplication.CreateBuilder(args);

BDD.DBContext.Configuration = builder.Configuration;

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.Cookie.Name = "CRI_Rodrigue.Cookie"; 
        options.ExpireTimeSpan = TimeSpan.FromMinutes(value: 60); 
        options.SlidingExpiration = true;
        options.LogoutPath = "/Pages/Utilisateur/deconnexion"; 
        options.LoginPath = "/Pages/Utilisateur/connexion"; 
    });

builder.Services.AddDistributedMemoryCache(); 
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(value: 30); 
    options.Cookie.HttpOnly = true; 
    options.Cookie.IsEssential = true;
});

builder.Services.AddSingleton<VerifTelephone>();


builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    var supportedCultures = new[] { "en-UK", "fr" , "de"};
    options.SetDefaultCulture(supportedCultures[0])
        .AddSupportedCultures(supportedCultures)
        .AddSupportedUICultures(supportedCultures);
});

//builder.Services.AddDbContext<DBEntityContext>(options =>
//    options.UseMySQL("ConnectionStrings:DBTest"));


builder.Services.AddLocalization(options =>
{
    options.ResourcesPath = "";
});

builder.Services.AddControllersWithViews()
                .AddSessionStateTempDataProvider(); 
builder.Services.AddRazorPages()
                .AddSessionStateTempDataProvider(); 
builder.Services.AddDataProtection(); 

var app = builder.Build();


if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler(errorHandlingPath: "/Error");
    app.UseHsts();
}

app.UseRequestLocalization(app.Services.GetRequiredService<IOptions<RequestLocalizationOptions>>().Value);

app.MapGet("Culture/Set", (string culture, string redirectUri, HttpContext context) =>
{
    if (culture is not null)
    {
        context.Response.Cookies.Append(CookieRequestCultureProvider.DefaultCookieName,
            CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture, culture)),
            new CookieOptions { Expires = DateTimeOffset.Now.AddDays(30) });
    }

    return Results.LocalRedirect(redirectUri);
});


app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseSession();

app.UseAuthentication();
app.UseAuthorization();

app.MapRazorPages();
app.MapControllers();

//    app.UseEndpoints(endpoints =>
//    {
//        endpoints.MapControllerRoute(
//           name: "default",
//pattern: "{controller=Home}/{action=Index}/{id?}");

//        endpoints.MapRazorPages();
//    });
app.Run();

