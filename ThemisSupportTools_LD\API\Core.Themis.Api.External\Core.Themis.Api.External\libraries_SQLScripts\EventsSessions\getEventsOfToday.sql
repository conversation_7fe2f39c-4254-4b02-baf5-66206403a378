
-- Sélectionne les manifestations d'aujourd'hui pour une durée donnée (12heures)
SELECT Distinct m.manifestation_id as eventId, manifestation_nom as eventName, seance_id as sessionId, seance_date_deb as sessionStartDate, 
l.lieu_nom as placeName,l.lieu_id as placeId, lc.lieu_config_id,lc.lieu_config_nom 
FROM seance s 
INNER JOIN manifestation m ON m.manifestation_id = s.manifestation_id
INNER JOIN lieu l ON l.lieu_id=s.lieu_id
 INNER JOIN lieu_configuration lc ON lc.lieu_config_id=s.lieu_config_id
WHERE
--s.seance_date_deb > getdate() AND 
s.seance_date_deb > dateadd(hour,-1,getdate()) and
s.seance_date_deb < DATEADD(MINUTE, @pDuration, GETDATE()) AND s.supprimer<>'O' and options='N' 

