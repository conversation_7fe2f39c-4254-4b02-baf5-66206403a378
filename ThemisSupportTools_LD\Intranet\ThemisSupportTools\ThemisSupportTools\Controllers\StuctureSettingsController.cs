﻿using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
namespace ThemisSupportTools.Controllers
{
    public class StuctureSettingsController : Controller
    {
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;

        public StuctureSettingsController(IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary)
        {
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
        }

        //A Retirer
        public IActionResult Index()
        {
            var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(int.Parse(structureId));

            return View();
        }

        [Parameter]
        public string structureId { get; set; }
    }
}
