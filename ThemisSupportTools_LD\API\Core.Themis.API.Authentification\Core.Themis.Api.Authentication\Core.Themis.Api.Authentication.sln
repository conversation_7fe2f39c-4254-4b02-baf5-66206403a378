﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33627.172
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Api.Authentication", "Core.Themis.Api.Authentication\Core.Themis.Api.Authentication.csproj", "{3F7F4A74-852C-4C36-B375-2B5BFABB9639}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{8FF2B5FB-9C55-4EC5-8C66-655041A5B1FD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{A274DB5D-D04F-47B7-8740-F6A234F07F8B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{C5DF73A8-C20C-4BAC-8639-5E10DA9C431E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{018F10A0-2BE4-4725-9B3F-6EA93575AAE8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{C8563756-37D9-48B8-8EE9-CDBA2EE3BAC4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3F7F4A74-852C-4C36-B375-2B5BFABB9639}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F7F4A74-852C-4C36-B375-2B5BFABB9639}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F7F4A74-852C-4C36-B375-2B5BFABB9639}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F7F4A74-852C-4C36-B375-2B5BFABB9639}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FF2B5FB-9C55-4EC5-8C66-655041A5B1FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FF2B5FB-9C55-4EC5-8C66-655041A5B1FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FF2B5FB-9C55-4EC5-8C66-655041A5B1FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FF2B5FB-9C55-4EC5-8C66-655041A5B1FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{A274DB5D-D04F-47B7-8740-F6A234F07F8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A274DB5D-D04F-47B7-8740-F6A234F07F8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A274DB5D-D04F-47B7-8740-F6A234F07F8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A274DB5D-D04F-47B7-8740-F6A234F07F8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5DF73A8-C20C-4BAC-8639-5E10DA9C431E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5DF73A8-C20C-4BAC-8639-5E10DA9C431E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5DF73A8-C20C-4BAC-8639-5E10DA9C431E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5DF73A8-C20C-4BAC-8639-5E10DA9C431E}.Release|Any CPU.Build.0 = Release|Any CPU
		{018F10A0-2BE4-4725-9B3F-6EA93575AAE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{018F10A0-2BE4-4725-9B3F-6EA93575AAE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{018F10A0-2BE4-4725-9B3F-6EA93575AAE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{018F10A0-2BE4-4725-9B3F-6EA93575AAE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C8563756-37D9-48B8-8EE9-CDBA2EE3BAC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8563756-37D9-48B8-8EE9-CDBA2EE3BAC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8563756-37D9-48B8-8EE9-CDBA2EE3BAC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8563756-37D9-48B8-8EE9-CDBA2EE3BAC4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {00A30937-BD7C-4C2B-8A1A-8701A7E2553C}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 7
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = Core.Themis.Api.Authentication\\Core.Themis.Api.Authentication.csproj
		SccProjectName1 = Core.Themis.Api.Authentication
		SccLocalPath1 = Core.Themis.Api.Authentication
		SccProjectUniqueName2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName2 = ../../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName3 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName4 = ../../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName5 = ../../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName6 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
	EndGlobalSection
EndGlobal
