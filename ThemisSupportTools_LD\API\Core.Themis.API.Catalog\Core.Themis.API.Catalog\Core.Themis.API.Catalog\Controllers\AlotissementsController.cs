﻿using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;


namespace Core.Themis.API.Catalog.Controllers
{

    [ApiController]
    public class AlotissementsController : ControllerBase
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IPlaceObjectManager _placeObjectManager;

        private static readonly RodrigueNLogger Logger = new();
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="memoryCache"></param>
        /// <param name="apiUtilities"></param>
        public AlotissementsController(
            IMemoryCache memoryCache, 
            IPlaceObjectManager placeObjectManager)
        {
            _memoryCache = memoryCache;
            _placeObjectManager = placeObjectManager;
        }

        /// <summary>
        /// list of all the alotissements
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<AlotissementDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]

        [Route("api/{structureId}/AlotissementsList/{codeLang}")]

        public IActionResult AlotissementsList(int structureId, string codeLang)
        {
            try
            {
                Logger.Debug(structureId, $"AlotissementsList({structureId},{codeLang}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<AlotissementDTO> listS = _placeObjectManager.GetAllAlotissements(structureId, codeLang);

                Logger.Debug(structureId, $"AlotissementsList({structureId},{codeLang}) ok {listS.Count} alotissements to return");

                return Ok(listS);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"AlotissementsList({structureId},{codeLang}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
