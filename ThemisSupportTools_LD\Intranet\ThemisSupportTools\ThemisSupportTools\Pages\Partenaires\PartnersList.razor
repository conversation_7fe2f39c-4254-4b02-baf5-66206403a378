﻿@page "/partners"

@using Core.Themis.Libraries.Razor.Common.Components


@inject NavigationManager Navigation
@inject IPartnerManager PartenairesManager
@inject IConfiguration _configuration

<Core.Themis.Libraries.Razor.Common.Components.Dialog @ref="DeleteConfirmation" EventChanged="ConfirmDelete_Click" ButtonValider="Confirmer" ButtonCancel="Annuler"
                                                      Message=@($"Etes-vous sûr de vouloir supprimer ce partenaire <strong>{partnerDTOSelected}</strong>")>
</Core.Themis.Libraries.Razor.Common.Components.Dialog>

<h3>Partners List </h3>
<div class="mt-2">

    <NavLink class="btn btn-primary" href="/create-partenaire">Nouveau Partenaire</NavLink>
</div>
<h3 style="text-align: center">Choisir un  partenaire</h3>






@if (_isLoading)
{
    <div class="spinner-border text-info" role="status">
        <span class="visually-hidden"></span>
    </div>
}
else
{

    <SearchBox OnSearch="@OnSearchAsync"></SearchBox>

    <div class="col-12">
        <div class="list-group">
            @foreach (var ptr in _partenaires)
            {
                <div class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true" data-id="47">
                    <div class="d-md-flex gap-2 w-100 text-center text-md-start">
                        <div>
                            <h6 class="mb-0"><strong>@ptr.PartnerName</strong></h6>

                        </div>
                        <div class="ms-auto d-flex align-items-center gap-2 justify-content-center justify-content-md-end">

                            <a class="btn btn-primary me-2" role="button" href="@PartenaireUrl(ptr.PartnerId)">
                                <i class="bi bi-pencil"></i>
                            </a>


                            <button class="btn btn-secondary"
                                    @onclick="() => DeletePartner(ptr)"
                                    @onclick:preventDefault>
                                Supprimer
                            </button>
                        </div>

                    </div>
                </div>
            }

        </div>
    </div>

}




@code {

    //BootstrapRazor.Dialog dialog = new Core.Themis.Libraries.Razor.Common.Components.Dialog();
    //Core.Themis.Libraries.Razor.Common.Components.ModalDialog.ModalDialog modalDialog = new Core.Themis.Libraries.Razor.Common.Components.ModalDialog.ModalDialog();
    protected Core.Themis.Libraries.Razor.Common.Components.Dialog DeleteConfirmation { get; set; } = new();
    private List<Core.Themis.Libraries.DTO.PartnerDTO> _partenaires = new();
    private bool _isLoading = true;
    //private ConfirmDialog _confirmDialog = default!;
    private PartnerDTO partnerDTOSelected = new();

    [Parameter]
    public string? SearchTerm { get; set; }

    public PartnerDTO? partenaire { get; set; }

    public string PathBase { get; set; } = string.Empty;


    private List<Core.Themis.Libraries.DTO.PartnerDTO> GetActivesPartenaires()
    {

        return PartenairesManager.GetAllPartners()
                            .Where(p => p.DateSupp is null)
                                .OrderBy(p => p.PartnerId).ToList();
    }


    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {

            PathBase = _configuration["PathBase"]!;

            _partenaires = GetActivesPartenaires();
            _isLoading = false;
            StateHasChanged();
        }
    }

    private static string PartenaireUrl(int PartnerId) => $"/editpartenaire/{PartnerId}";



    //private void Search()
    //{

    //    _partenaires = GetActivesPartenaires();
    //    var _filteredpartenaires = _partenaires.Where(p => p.PartnerName.ToLower().Contains(SearchTerm?.ToLower())).ToList();
    //    _partenaires = _filteredpartenaires;
    //    _isLoading = false;
    //    StateHasChanged();
    //}


    //private async Task ShowDeletePartnerConfirmationAsync()
    //{
    //    var options = new ConfirmDialogOptions
    //    {
    //        YesButtonText = "Supprimer",
    //        YesButtonColor = ButtonColor.Success,
    //        NoButtonText = "ANNULER",
    //        NoButtonColor = ButtonColor.Danger
    //    };

    //    if (await ShowConfirmationAsync(options, "Suppression d'un partenaire", "Etes-vous sûr de vouloir supprimer ce partenaire ?"))
    //    {

    //        ConfirmDelete_Click(deleteConfirmed: true);
    //    }
    //}


    //private async Task<bool> ShowConfirmationAsync(ConfirmDialogOptions dialogOptions, string title, string message)
    //{
    //    var confirmation = await _confirmDialog.ShowAsync(


    //        title: title,
    //        message1: message,
    //        confirmDialogOptions: dialogOptions

    //        );

    //    if (confirmation)
    //    {
    //        return true;
    //    }
    //    else
    //    {
    //        return false;
    //    }
    //}

    private void DeletePartner(PartnerDTO partner)
    {
        partnerDTOSelected = partner;
        DeleteConfirmation.Show();
    }
    protected void ConfirmDelete_Click(bool deleteConfirmed)
    {
        if (deleteConfirmed)
        {
            partnerDTOSelected.DateSupp = DateTime.Now;

            PartenairesManager.UpdatePartnerAsync(partnerDTOSelected);
            Navigation.NavigateTo("/partners", true);
        }

    }

    private async Task OnSearchAsync(string searchTerm)
    {
        var filteredPartenaires = await PartenairesManager.SearchPartnersAsync(searchTerm).ConfigureAwait(false);
        _partenaires = filteredPartenaires?.OrderBy(p => p.PartnerId).ToList();
    }



}



