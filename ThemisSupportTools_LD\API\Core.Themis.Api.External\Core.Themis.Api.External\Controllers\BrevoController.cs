﻿
using Core.Themis.Libraries.BLL.InfoComp.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Queue.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using static Core.Themis.API.External.Helpers.BrevoObjects;

namespace Core.Themis.API.External.Controllers
{
    /// <summary>
    ///  Brevo WebHook
    /// </summary>
    [ApiController]
    [SwaggerTag("Internal")]
    [ApiExplorerSettings(GroupName = "internal")]
    public class BrevoController : ControllerBase
    {

        private readonly IConfiguration _configuration;        
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IInfoCompManager _infoCompManager;

        private static readonly RodrigueNLogger Logger = new();
        private readonly IMemoryCache _memoryCache;

        public BrevoController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IInfoCompManager infoCompManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;

            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _infoCompManager = infoCompManager;
        }

        /// <summary>
        /// Update the infocomp with the new email event
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpPost]
        [Route("api/brevowebhook/{structureId}")]
        public async Task<IActionResult> BrevoWebHook(int structureId, [FromBody] WebHookItem item)
        {
            try
            {
                string clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                string BrevoIpRanges = _configuration["BrevoIpRanges"];

                bool isIpInRange = false;
                isIpInRange = IPHelper.IsIpInRangeList(clientIp, BrevoIpRanges);

                if (!isIpInRange)
                {
                    string errMsgDetails = $"BrevoWebHook({structureId}) Campaign_id={item.CampId} Email={item.Email} Event={item.Event} error";
                    Logger.Error(structureId, errMsgDetails + $" Your Ip {clientIp} is not authorized");
                    return Problem($"Access denied.  {errMsgDetails}", null, StatusCodes.Status401Unauthorized);
                }

                string itemJSON = Newtonsoft.Json.JsonConvert.SerializeObject(item);
                 Logger.Debug(structureId, $"BrevoWebHook({structureId}) Campaign_id={item.CampId} Email={item.Email} Event={item.Event} {itemJSON}");

                /* var accT = Request.Headers[HeaderNames.Authorization];
                 if (!TokenManager.PartnerHasRight(structureId, accT))
                     return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                */

                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);
                Campaign campaign;
                string ApiBrevoUrl = _configuration["ApiBrevoUrl"];
                string cacheName = "Campain:" + item.CampId.ToString();
                List<string> folders = new List<string>();
                string brevoApiKey = "";

                if (configIniXml.ContainsKey("EDCAMPAIGNBREVOKEY") && !string.IsNullOrEmpty(configIniXml["EDCAMPAIGNBREVOKEY"]))
                    brevoApiKey = configIniXml["EDCAMPAIGNBREVOKEY"].ToString();

                if (!_memoryCache.TryGetValue(cacheName, out campaign))// Look for cache key.
                {



                    var client = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Get, ApiBrevoUrl + "emailCampaigns/" + item.CampId.ToString());
                    request.Headers.Add("Accept", "application/json");



                    request.Headers.Add("api-key", brevoApiKey);


                    var response = await client.SendAsync(request);
                    response.EnsureSuccessStatusCode();

                    string jsonString = await response.Content.ReadAsStringAsync();
                    campaign = JsonConvert.DeserializeObject<Campaign>(jsonString);

                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                            .SetPriority(CacheItemPriority.Low)
                            .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));

                    _memoryCache.Set(cacheName, campaign, cacheEntryOptions);


                }

                string cacheFoldersName = "Folders:" + item.CampId.ToString();
                if (!_memoryCache.TryGetValue(cacheFoldersName, out folders))// Look for cache key.
                {

                    foreach (int id_list in campaign.recipients.lists)
                    {
                        Console.WriteLine(id_list);
                        var clientList = new HttpClient();
                        var requestList = new HttpRequestMessage(HttpMethod.Get, ApiBrevoUrl + "contacts/lists/" + id_list.ToString());
                        requestList.Headers.Add("Accept", "application/json");
                        requestList.Headers.Add("api-key", brevoApiKey);

                        var responseList = await clientList.SendAsync(requestList);
                        responseList.EnsureSuccessStatusCode();

                        string jsonStringList = await responseList.Content.ReadAsStringAsync();
                        ListResponse listResponse = JsonConvert.DeserializeObject<ListResponse>(jsonStringList);
                        if (folders == null) folders = new List<String>();
                        folders.Add("BREVO" + listResponse.FolderId.ToString());

                    }

                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetPriority(CacheItemPriority.Low)
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));

                    _memoryCache.Set(cacheFoldersName, folders, cacheEntryOptions);


                }


                string emailColumn = configIniXml["VARIABLESEMAIL"].ToString();
                bool ret = _infoCompManager.UpdateStats(structureId, item.Email, item.Event, emailColumn, folders);
                if (ret)
                {
                    Logger.Debug(structureId, $"BrevoWebHook({structureId}) Campaign_id={item.CampId} Email={item.Email} Event={item.Event} ok ");

                    return Ok();
                }
                else
                {
                    string errMsgDetails = $"BrevoWebHook({structureId}) Campaign_id={item.CampId} Email={item.Email} Event={item.Event} error";
                    Logger.Error(structureId, errMsgDetails);
                    return Problem($"Error during updating event in database. {errMsgDetails}", null, StatusCodes.Status500InternalServerError);
                }

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"BrevoWebHook({structureId}) Campaign_id={item.CampId} Email={item.Email} Event={item.Event} error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

    }
}
