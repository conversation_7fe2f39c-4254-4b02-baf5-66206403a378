﻿using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System.Collections.Generic;
using System;
using Core.Themis.Libraries.BLL.Interfaces;
using System.Linq;
using Core.Themis.Libraries.DTO.Offers;
using System.Threading.Tasks;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.BLL.Managers.Catalog.Interfaces;

namespace Core.Themis.API.Catalog.Controllers
{
    [ApiController]
    public class PricesController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IEventManager _eventManager;
        private readonly ICatalogManager _eventsCatalogManager;
        private readonly IOfferManager _offerManager;
        private readonly IAbonnementManager _abonnementManager;
        private static readonly RodrigueNLogger Logger = new();
        /// <summary>
        /// Constructeur
        /// </summary>
        public PricesController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IEventManager eventManager,
            IOfferManager offerManager,
            IAbonnementManager abonnementManager,
            ICatalogManager eventsCatalogManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _eventManager = eventManager;
            _offerManager = offerManager;
            _abonnementManager = abonnementManager;
            _eventsCatalogManager = eventsCatalogManager;
        }

        /// <summary>
        /// Insert les tarifs les plus bas dans les tables LowerPrice et LowerPriceAbo
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
       // [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]

        [Route("api/{structureId}/InsertLowestPrice")]

        public async Task<IActionResult> InsertLowestPrice(int structureId)
        {
            try
            {
                Logger.Debug(structureId, $"InsertLowestPrice({structureId}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);


                #region Lower prices INDIV
                var eventsOnSales = await _eventManager.GetAllEventsOnSales(structureId);
                IEnumerable<OfferDTO> offersOnSales = await _offerManager.GetOffersOnSales(structureId);


                List<int> eventsId = eventsOnSales.Select(e => e.EventId).ToList();
                List<int> offersId = offersOnSales.Select(o => o.OfferId).ToList();


                Logger.Debug(structureId, $"offersId {string.Join(",", offersId)} eventsId {string.Join(",", eventsId)}");

                await _eventsCatalogManager.InsertLowerPrice(structureId, offersId, eventsId, true);

                #endregion


                #region Lower prices Abo
                IEnumerable<FormulaDTO> formulesClosed = await _abonnementManager.GetAbonnementClosed(structureId);


                List<int> formulesId = formulesClosed.Select(f => f.FormulaId).ToList();
                Logger.Debug(structureId, $"formulesId {string.Join(",", formulesId)} ");

                await _eventsCatalogManager.InsertLowestPricesAboAsync(structureId, formulesId, true);
                #endregion



                return Ok();

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"InsertLowestPrice({structureId}) error {ex.Message} \n {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
