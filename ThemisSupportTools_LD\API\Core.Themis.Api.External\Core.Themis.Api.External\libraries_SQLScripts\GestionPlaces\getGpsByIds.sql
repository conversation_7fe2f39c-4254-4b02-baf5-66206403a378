/* getGpsByIds.sql */


DECLARE @const_placementlibre int; set @const_placementlibre =32;
DECLARE @const_priseauto int; set @const_priseauto =16;
DECLARE @const_vueplacement int; set @const_vueplacement =8;
DECLARE @const_choixsurplan int; set @const_choixsurplan =1;

SELECT convert(int,gp.gestion_place_id) as gestion_place_id,
manif_id ,
convert(int, seance_id) as seance_id,
gp.categ_id,
gp.type_tarif_id,
formule_id,
nb_max,
nb_min,
iscontrainteidentite,
dispo,
isvalide,
gestion_place_parent_id,
gp.aucune_reserve,
gpr.reserve_id,
r.reserve_nom as reserve_name,
r.reserve_code,
tt.type_tarif_nom,
cat.categ_nom,
o.offre_id, o.offre_nom,
adh.Adhesion_Catalog_ID,
adh.Catalog_Libelle
, 0 as vts_id /* todo */
 ,vueplacement = CASE WHEN (gp.prise_place & @const_vueplacement)=0 THEN 0 ELSE 1 END
FROM gestion_place gp

LEFT JOIN type_tarif tt on tt.type_tarif_id = gp.type_tarif_id

LEFT JOIN categorie cat on cat.categ_id = gp.categ_id
LEFT OUTER JOIN gestion_place_reserve gpr on gpr.gestion_place_id = gp.gestion_place_id
LEFT OUTER JOIN reserve r on gpr.reserve_id = r.reserve_id

LEFT JOIN offre_gestion_place ogp on ogp.gestion_place_id = gp.gestion_place_id
LEFT JOIN offre o on o.offre_id = ogp.offre_id
LEFT JOIN adhesion_catalog_offresliees adhoffre on adhoffre.offre_id = o.offre_id
LEFT JOIN adhesion_catalog adh on adhoffre.adhesion_catalog_id = adh.Adhesion_Catalog_ID

WHERE gp.gestion_place_id in ({gpids})