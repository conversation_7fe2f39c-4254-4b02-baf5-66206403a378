﻿using MiseEnVentes.ServiceUnitaire;
using Xunit;

namespace MiseEnVentesTests
{

    //Test pour modifier un intervenant 
    public class EditIntervenantManagerTests
    {
        [Fact]
        public void Should_Add_Intervenant_When_Checked_Is_True()
        {
            // Arrange
            var manager = new EditIntervenantManager();
            int id = 42;

            // Act
            manager.OnEditIntervenantCheckedChanged(id, true);

            // Assert
            Assert.Contains(id, manager.Model.Intervenants);
        }

        [Fact]
        public void Should_Remove_Intervenant_When_Checked_Is_False()
        {
            // Arrange
            var manager = new EditIntervenantManager();
            int id = 42;
            manager.Model.Intervenants.Add(id);

            // Act
            manager.OnEditIntervenantCheckedChanged(id, false);

            // Assert
            Assert.DoesNotContain(id, manager.Model.Intervenants);
        }

        [Fact]
        public void Should_Not_Add_Duplicate_Intervenant()
        {
            // Arrange
            var manager = new EditIntervenantManager();
            int id = 42;
            manager.Model.Intervenants.Add(id);

            // Act
            manager.OnEditIntervenantCheckedChanged(id, true);

            // Assert
            Assert.Single(manager.Model.Intervenants);
        }
    }
}
