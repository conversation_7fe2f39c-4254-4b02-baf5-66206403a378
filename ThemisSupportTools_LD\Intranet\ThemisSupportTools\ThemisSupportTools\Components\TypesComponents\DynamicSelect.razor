﻿
@if (!string.IsNullOrWhiteSpace(PlaceHolder))
{
    <option value="">@PlaceHolder</option>
}

@foreach (var item in SelectItems)
{
   <option value="@item.Value" selected="@item.IsSelected" disabled="@item.IsDisabled">@item.Text</option>
}

@code {

    [Parameter] public string PlaceHolder { get; set; } = string.Empty;
    [Parameter] public List<GenericSelectDataLookup> SelectItems { get; set; } = new();
    [Parameter] public string CssClass { get; set; } = string.Empty;    
}
