﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MiseEnVentes.Models
{
    public class MiseEnVentePlateforme
    {
        [Key]
        public int IdMiseEnVentePlateforme { get; set; }

        [ForeignKey("IdMiseEnVente")]
        public int IdMiseEnVente { get; set; }
        public MiseEnVenteNew MiseEnVente { get; set; } = null!;

        [ForeignKey("IdPlateforme")]
        public int IdPlateforme { get; set; }
        public Plateforme Plateforme { get; set; } = null!;
    }
}
