﻿@page "/structureslist"
@attribute [Authorize]

@using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
@using Core.Themis.Libraries.DTO.WSAdmin;
@using Core.Themis.Libraries.Razor.Common.Components
@using Core.Themis.Libraries.Utilities.Logging;
@inject NavigationManager Navigation
@inject ILogger<StructuresList> Logger
@inject IWsAdminStructuresManager StructuresManager
@inject RodrigueNLogger RodrigueNLogger


<h3 style="text-align: center">Choisir une structure</h3>
<div class="row">
    @if (_isLoading)
    {
        <div class="spinner-border text-info" role="status">
            <span class="visually-hidden"></span>
        </div>
    }
    else
    {

        <SearchBox OnSearch="@OnSearchAsync"></SearchBox>


        <div class="col-12">
            <div class="list-group">
                @foreach (var str in _structures)
                {
                    <div class="list-group-item list-group-item-action d-flex gap-3 py-3" aria-current="true" data-id="47">
                        <div class="d-md-flex gap-2 w-100 text-center text-md-start">
                            <div>
                                <h6 class="mb-0"><strong>@str.Name</strong></h6>
                                <p class="mb-0 opacity-75"><em>@str.StructureId</em></p>
                            </div>
                            <div class="ms-auto d-flex align-items-center gap-2 justify-content-center justify-content-md-end">
                                @foreach (var typeEnvir in _dictEnvironnements)
                                {
                                    if (typeEnvir.Key == "PROD") // on est en TEST ou DEV => empecher d'aller taper la PROD
                                    {
                                        <a class="disabled btn btn-secondary <EMAIL>">@typeEnvir.Key</a>
                                    }
                                    else
                                    {
                                        <a @onclick=@(() => SelectStructure(str.StructureId, typeEnvir.Key)) value="@typeEnvir" role="button" class="btn btn-secondary <EMAIL>">@typeEnvir.Key</a>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>



@code {
    private List<WsAdminStructureDTO> _structures = new();
    private bool _isLoading = true;

    public string SearchTerm { get; set; } = default!;

    private Dictionary<string, string> _dictEnvironnements = new()
    {
       {"DEV", "success"},
       { "TEST", "warning"},
       { "PROD", "danger"}
    };

    private async Task<List<WsAdminStructureDTO>> GetActivesStructuresAsync()
    {
        RodrigueNLogger.Debug(0, $"structureList GetStructuresList()...");

        var activesStructures = await StructuresManager.GetActivesWsAdminStructuresAsync().ConfigureAwait(false);
        return activesStructures.OrderBy(s => int.Parse(s.StructureId)).ToList();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _structures = await GetActivesStructuresAsync();
            _isLoading = false;
            StateHasChanged();
        }
    }

    void SelectStructure(string structureid, string environment)
    {
        Navigation.NavigateTo($"dashboardmodule/{structureid}/{environment}");
        //Navigation.NavigateTo($"structuresettings/{structureid}/{environment}");
    }


    private async Task OnSearchAsync(string searchTerm)
    {

        var filteredStructures = await StructuresManager.SearchWsAdminStructuresAsync(searchTerm).ConfigureAwait(false);
        _structures = filteredStructures.OrderBy(s => int.Parse(s.StructureId)).ToList();
    }

}
