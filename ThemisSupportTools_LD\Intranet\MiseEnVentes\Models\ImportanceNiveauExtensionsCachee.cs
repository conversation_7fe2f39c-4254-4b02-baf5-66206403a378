﻿using Microsoft.AspNetCore.Components;

namespace MiseEnVentes.Models
{
    public static class ImportanceNiveauExtensionsCachee
    {
        public static string GetLabel(this ImportanceNiveauCachee niveau) => niveau switch
        {
            ImportanceNiveauCachee.Normal => "🌶️",
            ImportanceNiveauCachee.Moyen => "🌶️🌶️",
            ImportanceNiveauCachee.Eleve => "🌶️🌶️🌶️",
            _ => "Inconnu"
        };

        public static string GetEmoji(this ImportanceNiveauCachee niveau) => niveau switch
        {
            ImportanceNiveauCachee.Normal => "",
            ImportanceNiveauCachee.Moyen => "",
            ImportanceNiveauCachee.Eleve => "",
            _ => "text-secondary"
        };
    }
}
