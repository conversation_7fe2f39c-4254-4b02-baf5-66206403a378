﻿/*using MiseEnVentes;
using MiseEnVentes.Interfaces;
using MiseEnVentes.Models;
using Moq;
using Moq.EntityFrameworkCore;

public class MiseEnVenteServiceTests
{
    [Theory]
    [InlineData("StructureA", "2025-06-06", "13:00", true, false, "commentaire A", "42", new[] { 1, 2 })]
    [InlineData("StructureB", "2025-07-10", "09:30", false, true, "commentaire B", null, null)]
    public async Task HandleValidSubmit_Should_Save_Vente_And_Associations(
        string structureName,
        string dateStr,
        string heure,
        bool mail,
        bool ticket,
        string commentaire,
        string? plateformeId,
        int[]? intervenantsIds)
    {
        // Arrange
        var dbMock = new Mock<IApplicationDbContext>();
        var ventes = new List<MiseEnVenteNew>();
        var plateformes = new List<MiseEnVentePlateforme>();
        var intervenants = new List<MiseEnVenteIntervenant>();

        dbMock.Setup(x => x.MisesEnVenteNew).ReturnsDbSet(ventes);
        dbMock.Setup(x => x.MiseEnVentePlateformes).ReturnsDbSet(plateformes);
        dbMock.Setup(x => x.MiseEnVenteIntervenants).ReturnsDbSet(intervenants);

        var service = new Mock<MiseEnVenteService>(dbMock.Object) { CallBase = true };
        bool toastCalled = false;
        service.Setup(s => s.ShowToast()).Returns(() =>
        {
            toastCalled = true;
            return Task.CompletedTask;
        });

        var structureId = Guid.NewGuid();
        service.Object.AccountBases = new Dictionary<Guid, string?>
            {
                { Guid.NewGuid(), "StructureA" },
                { Guid.NewGuid(), "StructureB" }
            };


        service.Object.formModel = new FormModel
        {
            Structure = structureName,
            Date = DateTime.Parse(dateStr),
            Heure = heure,
            Mail = mail,
            Ticket = ticket,
            Commentaire = commentaire,
            Plateforme = plateformeId,
            Intervenants = intervenantsIds?.ToList()
        };

        // Act
        await service.Object.HandleValidSubmit();

        // Assert
        Assert.Single(ventes);
        var vente = ventes[0];
        Assert.Equal(commentaire, vente.Commentaire);
        Assert.Equal(mail, vente.NotifMail);
        Assert.Equal(ticket, vente.NotifTicket);

        if (plateformeId != null)
            Assert.Single(plateformes);
        else
            Assert.Empty(plateformes);

        if (intervenantsIds != null)
            Assert.Equal(intervenantsIds.Length, intervenants.Count);
        else
            Assert.Empty(intervenants);

        Assert.False(service.Object.showPopup);
        Assert.True(toastCalled);
        dbMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Exactly(2));
    }
}*/
