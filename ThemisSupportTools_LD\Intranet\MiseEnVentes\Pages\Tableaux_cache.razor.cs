﻿using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.AspNetCore.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using MiseEnVentes.Data;
using MiseEnVentes.Models;


using MiseEnVentes.Shared;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using static Core.Themis.Libraries.Utilities.Helpers.configIniSection;


namespace MiseEnVentes.Pages
{
    public partial class Tableaux_caché : ComponentBase
    {
        public List<MiseEnVenteNew> misesEnVente = new();
        public List<Intervenant>? intervenants;
        public List<Plateforme>? plateformees;
        public MiseEnVenteNew? venteToDelete;


        [Inject]
        public DynamicsDbContext DynamicsDb { get; set; } = default!;
        protected Dictionary<Guid, AccountBase> AccountBases { get; set; } = new();
        [Inject]
        public ApplicationDbContext Db { get; set; } = default!;

        [Inject]
        public IOptions<MiseEnVenteConfig> Config { get; set; } = null!;

        [Inject]
        public IOptions<TimerSettings> TimerOptions { get; set; } = null!;

        protected System.Threading.Timer? _timer;

        protected Dictionary<int, string> fileAttenteTypeByStructure = new();

        protected DateTime lastRefreshTime = DateTime.Now;



        protected override async Task OnAfterRenderAsync(bool firstRender)
        {

            if (firstRender)
            {
                await LoadDataAsync();

                var settings = TimerOptions.Value;

                _timer = new System.Threading.Timer(async _ =>
                {
                    await InvokeAsync(async () =>
                    {
                        await LoadDataAsync();
                        lastRefreshTime = DateTime.Now;
                        StateHasChanged();
                    });
                },
                null,
                TimeSpan.FromSeconds(settings.InitialDelaySeconds),
                TimeSpan.FromSeconds(settings.IntervalSeconds));

            }

            //return base.OnAfterRenderAsync(firstRender);
        }


        // Charge les données nécessaires pour la page de tableaux cachés
        private async Task LoadDataAsync()
        {
            // Récupère la date du jour
            var today = DateOnly.FromDateTime(DateTime.Today);
            // Récupère le délai d'expiration en heures depuis la configuration
            var delay = Config.Value.DelaiExpirationHeures;
            // Heure actuelle
            var now = TimeOnly.FromDateTime(DateTime.Now);
            // Calcule le seuil d'heure à partir du délai
            var seuil = now.AddHours(-delay);

            // Charge les mises en vente récentes (date >= aujourd'hui et heure >= seuil OU date > aujourd'hui)
            misesEnVente = await Db.MisesEnVenteNew.AsNoTracking()
                .Where(m => m.Date >= today && m.Hour >= seuil || m.Date > today)
                .Include(m => m.MiseEnVenteIntervenants)
                    .ThenInclude(mi => mi.Intervenant)
                .Include(m => m.MiseEnVentePlateformes)
                    .ThenInclude(mp => mp.Plateforme)
                .OrderBy(m => m.Date).Take(20)
                .ToListAsync();

            // Charge la liste des intervenants triés par nom
            intervenants = await Db.Intervenants
               .OrderBy(i => i.IntervenantName)
               .ToListAsync();

            // Charge la liste des plateformes triées par nom
            plateformees = await Db.Plateformes
                .OrderBy(i => i.PlateformeName)
                .ToListAsync();

            // Charge les comptes Dynamics dont le nom et l'ID structure sont renseignés
            AccountBases = await DynamicsDb.AccountBases
             .Where(a => a.Name != null && a.jav_IDStructure != null)
             .ToDictionaryAsync(a => a.AccountId, a => a);

            // Pour chaque mise en vente, récupère le type de file d'attente selon la structure
            foreach (var vente in misesEnVente)
            {
                if (Guid.TryParse(vente.StructureId, out Guid guid)
                    && AccountBases.TryGetValue(guid, out var account)
                    && int.TryParse(account.jav_IDStructure, out int idStruct)
                    && !fileAttenteTypeByStructure.ContainsKey(idStruct))
                {
                    // Récupère le type depuis la config et l'ajoute au dictionnaire
                    var type = configIniSection.GetEtatFromConfig(idStruct);
                    fileAttenteTypeByStructure[idStruct] = type;
                }
            }
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }

    }
}
