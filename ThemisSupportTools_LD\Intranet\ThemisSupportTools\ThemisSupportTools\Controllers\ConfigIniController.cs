﻿using Microsoft.AspNetCore.Mvc;
using System.Globalization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;


namespace ThemisSupportTools.Controllers
{
    public class ConfigIniController : Controller
    {
       
        public partial class Welcome4
        {
            [JsonProperty("configIni")]
            public ConfigIni ConfigIni { get; set; }
        }

        public partial class ConfigIni
        {
            [JsonProperty("Section")]
            public Section[] Section { get; set; }
        }

        public partial class Section
        {
            [JsonProperty("MONTANT_GRATUIT", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse MontantGratuit { get; set; }

            [JsonProperty("MONTANT_PRODUIT_GRATUIT", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse MontantProduitGratuit { get; set; }

            [JsonProperty("TELEPHONE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Telephone { get; set; }

            [JsonProperty("TYPE_TELEPHONE", NullValueHandling = NullValueHandling.Ignore)]
            public string TypeTelephone { get; set; }

            [JsonProperty("FAX", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Fax { get; set; }

            [JsonProperty("TYPE_FAX", NullValueHandling = NullValueHandling.Ignore)]
            public string TypeFax { get; set; }

            [JsonProperty("PORTABLE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Portable { get; set; }

            [JsonProperty("TYPE_PORTABLE", NullValueHandling = NullValueHandling.Ignore)]
            public string TypePortable { get; set; }

            [JsonProperty("EMAIL", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Email { get; set; }

            [JsonProperty("TYPE_EMAIL", NullValueHandling = NullValueHandling.Ignore)]
            public string TypeEmail { get; set; }

            [JsonProperty("_Name")]
            public string Name { get; set; }

            [JsonProperty("WEBFILIEREID", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Webfiliereid { get; set; }

            [JsonProperty("MAILUNICITY", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Mailunicity { get; set; }

            [JsonProperty("ALGO_CHOIXMANIF", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? AlgoChoixmanif { get; set; }

            [JsonProperty("NEW_CHOIXMANIF", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse NewChoixmanif { get; set; }

            [JsonProperty("Confirmation", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Confirmation { get; set; }

            [JsonProperty("Inscription", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Inscription { get; set; }

            [JsonProperty("ModifierProfil", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? ModifierProfil { get; set; }

            [JsonProperty("INFOCOMP", NullValueHandling = NullValueHandling.Ignore)]
            public string Infocomp { get; set; }

            [JsonProperty("DateVente", NullValueHandling = NullValueHandling.Ignore)]
            public string DateVente { get; set; }

            [JsonProperty("MessageInterdictionVente", NullValueHandling = NullValueHandling.Ignore)]
            public string MessageInterdictionVente { get; set; }

            [JsonProperty("GroupeFormuleLibelle", NullValueHandling = NullValueHandling.Ignore)]
            public string GroupeFormuleLibelle { get; set; }

            [JsonProperty("lbFormule", NullValueHandling = NullValueHandling.Ignore)]
            public string LbFormule { get; set; }

            [JsonProperty("lblTarif", NullValueHandling = NullValueHandling.Ignore)]
            public string LblTarif { get; set; }

            [JsonProperty("GrilleTarif", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? GrilleTarif { get; set; }

            [JsonProperty("FichierTarif", NullValueHandling = NullValueHandling.Ignore)]
            public string FichierTarif { get; set; }

            [JsonProperty("ModesReglement", NullValueHandling = NullValueHandling.Ignore)]
            public string ModesReglement { get; set; }

            [JsonProperty("IDMarchand", NullValueHandling = NullValueHandling.Ignore)]
            public string IdMarchand { get; set; }

            [JsonProperty("ReturnContext", NullValueHandling = NullValueHandling.Ignore)]
            public string ReturnContext { get; set; }

            [JsonProperty("WebOperatorID", NullValueHandling = NullValueHandling.Ignore)]
            public string WebOperatorId { get; set; }

            [JsonProperty("WebPostID", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? WebPostId { get; set; }

            [JsonProperty("MERCHANTCOUNTRY", NullValueHandling = NullValueHandling.Ignore)]
            public string Merchantcountry { get; set; }

            [JsonProperty("TextMerci", NullValueHandling = NullValueHandling.Ignore)]
            public string TextMerci { get; set; }

            [JsonProperty("URL_RETOURERR", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourerr { get; set; }

            [JsonProperty("URL_RETOUR_ABO", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourAbo { get; set; }

            [JsonProperty("URL_RETOURERR_ABO", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourerrAbo { get; set; }

            [JsonProperty("URL_RETOURANNUL_ABO", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourannulAbo { get; set; }

            [JsonProperty("URL_RETOUR_VENTE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourVente { get; set; }

            [JsonProperty("URL_RETOURERR_VENTE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourerrVente { get; set; }

            [JsonProperty("URL_RETOURANNUL_VENTE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourannulVente { get; set; }

            [JsonProperty("URL_RETOUR_GROUPE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourGroupe { get; set; }

            [JsonProperty("URL_RETOURERR_GROUPE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourerrGroupe { get; set; }

            [JsonProperty("URL_RETOURANNUL_GROUPE", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetourannulGroupe { get; set; }

            [JsonProperty("currency", NullValueHandling = NullValueHandling.Ignore)]
            public string Currency { get; set; }

            [JsonProperty("PERIODE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Periode { get; set; }

            [JsonProperty("NBPAYMENT", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Nbpayment { get; set; }

            [JsonProperty("PaiementEnXFois", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? PaiementEnXFois { get; set; }

            [JsonProperty("MONTANTMINNBPAYMENT", NullValueHandling = NullValueHandling.Ignore)]
            public Extensionbanner Montantminnbpayment { get; set; }

            [JsonProperty("FILIERENPAYMENT", NullValueHandling = NullValueHandling.Ignore)]
            public Extensionbanner Filierenpayment { get; set; }

            [JsonProperty("ASYNCHRONE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(FluffyParseStringConverter))]
            public bool? Asynchrone { get; set; }

            [JsonProperty("link", NullValueHandling = NullValueHandling.Ignore)]
            public Uri Link { get; set; }

            [JsonProperty("identifiant", NullValueHandling = NullValueHandling.Ignore)]
            public string Identifiant { get; set; }

            [JsonProperty("langage", NullValueHandling = NullValueHandling.Ignore)]
            public string Langage { get; set; }

            [JsonProperty("key", NullValueHandling = NullValueHandling.Ignore)]
            public string Key { get; set; }

            [JsonProperty("site", NullValueHandling = NullValueHandling.Ignore)]
            public string Site { get; set; }

            [JsonProperty("rang", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Rang { get; set; }

            [JsonProperty("version", NullValueHandling = NullValueHandling.Ignore)]
            public string SectionVersion { get; set; }

            [JsonProperty("mode", NullValueHandling = NullValueHandling.Ignore)]
            public string Mode { get; set; }

            [JsonProperty("pathexe", NullValueHandling = NullValueHandling.Ignore)]
            public string Pathexe { get; set; }

            [JsonProperty("login", NullValueHandling = NullValueHandling.Ignore)]
            public string Login { get; set; }

            [JsonProperty("password", NullValueHandling = NullValueHandling.Ignore)]
            public string Password { get; set; }

            [JsonProperty("DELAIREDIRECTIONAUTO", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Delairedirectionauto { get; set; }

            [JsonProperty("URL_RETOUR", NullValueHandling = NullValueHandling.Ignore)]
            public Uri UrlRetour { get; set; }

            [JsonProperty("LANGUE", NullValueHandling = NullValueHandling.Ignore)]
            public string Langue { get; set; }

            [JsonProperty("DEVISE_CODE", NullValueHandling = NullValueHandling.Ignore)]
            public string DeviseCode { get; set; }

            [JsonProperty("DEVISE_ISO", NullValueHandling = NullValueHandling.Ignore)]
            public string DeviseIso { get; set; }

            [JsonProperty("DEVISE_BEFORE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(FluffyParseStringConverter))]
            public bool? DeviseBefore { get; set; }

            [JsonProperty("DEVISE_SEPARATOR", NullValueHandling = NullValueHandling.Ignore)]
            public string DeviseSeparator { get; set; }

            [JsonProperty("MULTILANGUE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Multilangue { get; set; }

            [JsonProperty("LANGUE_EN", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? LangueEn { get; set; }

            [JsonProperty("LANGUE_IT", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? LangueIt { get; set; }

            [JsonProperty("LANGUE_SP", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? LangueSp { get; set; }

            [JsonProperty("LANGUE_DE", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? LangueDe { get; set; }

            [JsonProperty("PrestatairePaiement", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse PrestatairePaiement { get; set; }

            [JsonProperty("BANNERNAME", NullValueHandling = NullValueHandling.Ignore)]
            public string Bannername { get; set; }

            [JsonProperty("EXTENSIONBANNER", NullValueHandling = NullValueHandling.Ignore)]
            public Extensionbanner Extensionbanner { get; set; }

            [JsonProperty("ID", NullValueHandling = NullValueHandling.Ignore)]
            public Id? Id { get; set; }

            [JsonProperty("XFOIS", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Xfois { get; set; }

            [JsonProperty("IP", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Ip { get; set; }

            [JsonProperty("AccesFchoixseance", NullValueHandling = NullValueHandling.Ignore)]
            public string AccesFchoixseance { get; set; }

            [JsonProperty("Panier", NullValueHandling = NullValueHandling.Ignore)]
            public string Panier { get; set; }

            [JsonProperty("ShowUnavailablesSeats", NullValueHandling = NullValueHandling.Ignore)]
            public string ShowUnavailablesSeats { get; set; }

            [JsonProperty("StartImagesFauteuilsX", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? StartImagesFauteuilsX { get; set; }

            [JsonProperty("StartImagesFauteuilsY", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? StartImagesFauteuilsY { get; set; }

            [JsonProperty("RestrictionTarifParFichier", NullValueHandling = NullValueHandling.Ignore)]
            public Printathome? RestrictionTarifParFichier { get; set; }

            [JsonProperty("PRINTATHOME", NullValueHandling = NullValueHandling.Ignore)]
            public Printathome? Printathome { get; set; }

            [JsonProperty("CONTROLPRICE", NullValueHandling = NullValueHandling.Ignore)]
            public string Controlprice { get; set; }

            [JsonProperty("SENDERADRESSE", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Senderadresse { get; set; }

            [JsonProperty("REPLYADRESSE", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Replyadresse { get; set; }

            [JsonProperty("COPYADRESSE", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse Copyadresse { get; set; }

            [JsonProperty("DateDebut", NullValueHandling = NullValueHandling.Ignore)]
            public string DateDebut { get; set; }

            [JsonProperty("DateFin", NullValueHandling = NullValueHandling.Ignore)]
            public string DateFin { get; set; }

            [JsonProperty("Message", NullValueHandling = NullValueHandling.Ignore)]
            public string Message { get; set; }

            [JsonProperty("NextSeason", NullValueHandling = NullValueHandling.Ignore)]
            public Copyadresse NextSeason { get; set; }

            [JsonProperty("OFFREIDHORSABO", NullValueHandling = NullValueHandling.Ignore)]
            [JsonConverter(typeof(PurpleParseStringConverter))]
            public long? Offreidhorsabo { get; set; }

            [JsonProperty("VERSION", NullValueHandling = NullValueHandling.Ignore)]
            public string Version { get; set; }
        }

        public partial class Copyadresse
        {
            [JsonProperty("_del")]
            public Printathome Del { get; set; }

            [JsonProperty("_view")]
            public Printathome View { get; set; }

            [JsonProperty("_update")]
            public Printathome Update { get; set; }

            [JsonProperty("_commentaire")]
            public string Commentaire { get; set; }

            [JsonProperty("__text")]
            public string Text { get; set; }
        }

        public partial class Extensionbanner
        {
            [JsonProperty("_commentaire")]
            public string Commentaire { get; set; }

            [JsonProperty("__text")]
            public string Text { get; set; }
        }

        public enum Printathome { N, O };

        public partial struct Id
        {
            public Copyadresse Copyadresse;
            public string String;

            public static implicit operator Id(Copyadresse Copyadresse) => new Id { Copyadresse = Copyadresse };
            public static implicit operator Id(string String) => new Id { String = String };
        }

        public partial class Welcome4
        {
            public static Welcome4 FromJson(string json) => JsonConvert.DeserializeObject<Welcome4>(json, Converter.Settings);
        }


        internal static class Converter
        {
            public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
            {
                MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
                DateParseHandling = DateParseHandling.None,
                Converters =
            {
                PrintathomeConverter.Singleton,
                IdConverter.Singleton,
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
            };
        }

        internal class PurpleParseStringConverter : JsonConverter
        {
            public override bool CanConvert(Type t) => t == typeof(long) || t == typeof(long?);

            public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
            {
                if (reader.TokenType == JsonToken.Null) return null;
                var value = serializer.Deserialize<string>(reader);
                long l;
                if (Int64.TryParse(value, out l))
                {
                    return l;
                }
                throw new Exception("Cannot unmarshal type long");
            }

            public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
            {
                if (untypedValue == null)
                {
                    serializer.Serialize(writer, null);
                    return;
                }
                var value = (long)untypedValue;
                serializer.Serialize(writer, value.ToString());
                return;
            }

            public static readonly PurpleParseStringConverter Singleton = new PurpleParseStringConverter();
        }

        internal class FluffyParseStringConverter : JsonConverter
        {
            public override bool CanConvert(Type t) => t == typeof(bool) || t == typeof(bool?);

            public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
            {
                if (reader.TokenType == JsonToken.Null) return null;
                var value = serializer.Deserialize<string>(reader);
                bool b;
                if (Boolean.TryParse(value, out b))
                {
                    return b;
                }
                throw new Exception("Cannot unmarshal type bool");
            }

            public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
            {
                if (untypedValue == null)
                {
                    serializer.Serialize(writer, null);
                    return;
                }
                var value = (bool)untypedValue;
                var boolString = value ? "true" : "false";
                serializer.Serialize(writer, boolString);
                return;
            }

            public static readonly FluffyParseStringConverter Singleton = new FluffyParseStringConverter();
        }

        internal class PrintathomeConverter : JsonConverter
        {
            public override bool CanConvert(Type t) => t == typeof(Printathome) || t == typeof(Printathome?);

            public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
            {
                if (reader.TokenType == JsonToken.Null) return null;
                var value = serializer.Deserialize<string>(reader);
                switch (value)
                {
                    case "N":
                        return Printathome.N;
                    case "O":
                        return Printathome.O;
                }
                throw new Exception("Cannot unmarshal type Printathome");
            }

            public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
            {
                if (untypedValue == null)
                {
                    serializer.Serialize(writer, null);
                    return;
                }
                var value = (Printathome)untypedValue;
                switch (value)
                {
                    case Printathome.N:
                        serializer.Serialize(writer, "N");
                        return;
                    case Printathome.O:
                        serializer.Serialize(writer, "O");
                        return;
                }
                throw new Exception("Cannot marshal type Printathome");
            }

            public static readonly PrintathomeConverter Singleton = new PrintathomeConverter();
        }

        internal class IdConverter : JsonConverter
        {
            public override bool CanConvert(Type t) => t == typeof(Id) || t == typeof(Id?);

            public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
            {
                switch (reader.TokenType)
                {
                    case JsonToken.String:
                    case JsonToken.Date:
                        var stringValue = serializer.Deserialize<string>(reader);
                        return new Id { String = stringValue };
                    case JsonToken.StartObject:
                        var objectValue = serializer.Deserialize<Copyadresse>(reader);
                        return new Id { Copyadresse = objectValue };
                }
                throw new Exception("Cannot unmarshal type Id");
            }

            public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
            {
                var value = (Id)untypedValue;
                if (value.String != null)
                {
                    serializer.Serialize(writer, value.String);
                    return;
                }
                if (value.Copyadresse != null)
                {
                    serializer.Serialize(writer, value.Copyadresse);
                    return;
                }
                throw new Exception("Cannot marshal type Id");
            }

            public static readonly IdConverter Singleton = new IdConverter();
        }
    }


    public static class Serialize
    {
        public static string ToJson(this ConfigIniController self) => JsonConvert.SerializeObject(self, Converter.Settings);
    }
    internal static class Converter
    {
        public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
        {
            MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
            DateParseHandling = DateParseHandling.None,
            Converters =
            {
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
        };
    }
}
