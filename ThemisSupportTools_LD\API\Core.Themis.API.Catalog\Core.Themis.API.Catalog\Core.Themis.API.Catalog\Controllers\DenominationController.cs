﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System;

namespace Core.Themis.API.Catalog.Controllers
{

    [ApiController]
    public class DenominationController : ControllerBase
    {

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly IPlaceObjectManager _placeObjectManager;

        private static readonly RodrigueNLogger Logger = new();
     
        public DenominationController(
            IConfiguration configuration, 
            IMemoryCache memoryCache, 
            IApiUtilities apiUtilities,
            IPlaceObjectManager placeObjectManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _placeObjectManager = placeObjectManager;
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<DenominationDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]

        [Route("api/{structureId}/DenominationList/{codeLang}")]

        public IActionResult DenominationList(int structureId, string codeLang)
        {
            try
            {
                Logger.Debug(structureId, $"DenominationList({structureId},{codeLang}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<DenominationDTO> listS = _placeObjectManager.GetAllDenominations(structureId, codeLang);

                Logger.Debug(structureId, $"DenominationList({structureId},{codeLang}) ok {listS.Count} reserves to return");

                return Ok(listS);

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"DenominationList({structureId},{codeLang}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
