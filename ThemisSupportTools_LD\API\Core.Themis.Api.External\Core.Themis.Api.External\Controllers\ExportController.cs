﻿using Core.Themis.Libraries.BLL.Managers.Export.Interfaces;
using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
using Core.Themis.Libraries.DTO.ExportData;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Data.SqlClient;
using System.Net;
using System.Text;

namespace Core.Themis.Api.External.Controllers
{

    public class SwaggerControllerOrderAttribute : Attribute
    {
        public uint Order { get; }

        public SwaggerControllerOrderAttribute(uint order)
        {
            Order = order;
        }
    }

    /// <summary>
    /// 98 export : Get specific datas
    /// </summary>
    ///
    [SwaggerControllerOrder(99)]
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class ExportController : ControllerBase
    {
        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        private readonly IExportManager _exportManager;
        //private readonly IReservesManager _reservesManager;
        private readonly ILogsPartnerManager _logsPartnerManager;

        /// <summary>
        /// Controller for export data
        /// </summary>
        /// <param name="exportManager"></param>
        /// <param name="logsPartnerManager"></param>
        public ExportController(IExportManager exportManager,
            ILogsPartnerManager logsPartnerManager
            )
        {
            _exportManager = exportManager;
            //_reservesManager = reservesManager;
            _logsPartnerManager = logsPartnerManager;
        }

        /// <summary>
        /// Get export data by script name
        /// </summary>
        /// <param name="formData"></param>
        /// <returns></returns> 
        /// <remarks>
        /// 
        /// Caching 15s
        /// 
        /// Use POST if the framework can't allow body with a GET request
        /// </remarks>

        [HttpGet]
        [HttpPost]
        [Route("api/[controller]/GetExportByScriptName")]
        [Authorize(Roles = "Admin,Export")]
        [ResponseCache(Location = ResponseCacheLocation.Any, Duration = 5)]

        public ActionResult<string> GetExportByScriptName([FromBody] GenericFormExportModel formData)
        {
            try
            {
                string formDataToJson = JsonConvert.SerializeObject(formData);
                Logger.Info(0, $"formData:{formDataToJson}");

                Logger.Debug(formData.StructureId, $"GetExportByScriptName {JsonConvert.SerializeObject(formData)}...");

                string dataItem = $"processing\n\n";
                byte[] dataItemBytes = ASCIIEncoding.ASCII.GetBytes(dataItem);
                // await Response.Body.WriteAsync(dataItemBytes, 0, dataItemBytes.Length);

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(formData.StructureId, accT))
                    return Unauthorized($"structureId {formData.StructureId} is not allowed");

                int idPartner = TokenManager.getPartnerIdFromToken(accT);


                string? jsonResult = _exportManager.GetExportByScriptName(formData.StructureId, formData.ScriptName, 
                    formData.GetParamsDictionary(), idPartner);
                int jsonResultLength = System.Text.ASCIIEncoding.ASCII.GetByteCount(jsonResult!);
                var parentFolder = Directory.GetParent(AppContext.BaseDirectory)!.Name;



                Logger.Debug(formData.StructureId, $"result ok : length={(string.IsNullOrEmpty(jsonResult) ? 0 : jsonResult.Length)}");

                LogsPartenaireDTO dto = new LogsPartenaireDTO()
                {
                    PartnerId = idPartner,
                    StructureId = $"{formData.StructureId:0000}",
                    Message = "GetExportByScriptName," + formDataToJson,
                    ResponseLength = jsonResultLength,
                    Version = parentFolder
                };

                _logsPartnerManager.insert(dto);
                ////////////// enregister taille jsonresult
                //_logsPartnerManager.insert(idPartner, $"{formData.StructureId:0000}", jsonResult, jsonResultLength, parentFolder);


                return Ok(jsonResult);
            }
            catch (FileNotFoundException ex)
            {
                Logger.Error(0, ex.Message + " " + ex.StackTrace);
                return Problem(formData.ScriptName, null, (int)HttpStatusCode.NotFound);
            }
            catch (SqlException ex)
            {
                Logger.Error(0, ex.Message + " " + ex.StackTrace);
                return Problem(ex.Message, null, (int)HttpStatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex.Message + " " + ex.StackTrace);
                return Problem(ex.Message, null, (int)HttpStatusCode.InternalServerError);
            }
        }
    }
}
