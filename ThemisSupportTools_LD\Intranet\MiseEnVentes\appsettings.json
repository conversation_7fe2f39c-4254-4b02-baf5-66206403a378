{"ConnectionStrings": {"DefaultConnection": "Server=*************;Database=RODRIGUE_MISESENVENTES;Trusted_Connection=True;TrustServerCertificate=True;", "DefaultConnection_for_structure": "Data Source=*************;Initial Catalog=RODRIGUE_MSCRM_TEST;User ID=sa;Password=****************************"}, "ConfigIniPath": "\\\\*************\\customerfiles\\PROD\\[idstructure]\\CONFIGSERVER\\config.ini.xml", "MiseEnVenteConfig": {"DelaiExpirationHeures": 3}, "TimerSettings": {"InitialDelaySeconds": 0, "IntervalSeconds": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}