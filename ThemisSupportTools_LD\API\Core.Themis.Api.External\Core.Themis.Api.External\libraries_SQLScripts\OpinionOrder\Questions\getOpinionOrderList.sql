﻿
/*
declare @pOrderId int,
@pBasketId int, @pStructureId int



set @pStructureId = 281
set @pBasketId = 70440
set @pOrderId = 0

*/


--resultat de la requete dans open
-- id si la commande existe sinon null

declare @myCmdExist int
set @myCmdExist = null



DECLARE @SQL nvarchar(max) 
set @SQL = 'SELECT  @myCmdExist= COUNT(*) FROM OpinionOrderForms WHERE  (comment <> ''autoFill'' OR comment is null) and structure_id = '+CONVERT(VARCHAR(50),@pStructureId)

IF @pOrderId <> '' and @pOrderId > 0
begin
  set @SQL = @SQL +  ' and order_id='+CONVERT(VARCHAR(50), @pOrderId)

end


IF @pBasketId <> '' and @pBasketId > 0
begin
  set @SQL = @SQL +  ' and basket_id='+CONVERT(VARCHAR(50), @pBasketId)

end

exec sp_executesql @SQL, N'@myCmdExist int out', @myCmdExist out
--select @myCmdExist



DECLARE @SQL2 nvarchar(max) 

IF @myCmdExist = 1
  BEGIN

	set @SQL2 = 'select 
	ISNULL(frm.order_id, -1) as order_id_form,
	frm.basket_id,
	s.response_id as opinion_order_response_id,
	s.question_id,
	resp.score,
	frm.date_response,
	frm.comment,
	frm.form_id as form_id,
	s.question_type_id,
	s.question_type_code,
	s.question_type_comment,
	s.question_code
	from
	(
	select 
		(select frm.form_id from OpinionOrderForms frm where (comment <> ''autoFill'' OR comment is null) and frm.structure_id ='+ convert(varchar(50), @pStructureId) 
		
		
IF @pOrderId <> '' and @pOrderId > 0
begin
  set @SQL2 = @SQL2 +  ' and frm.order_id ='+CONVERT(VARCHAR(50), @pOrderId)
end


IF @pBasketId <> '' and @pBasketId > 0
begin
  set @SQL2 = @SQL2 +  ' and frm.basket_id='+CONVERT(VARCHAR(50), @pBasketId)
end
		
		
		
		set @SQL2 = @SQL2 +  ' ) as form_id,
		(select top 1 resp.reponse_id from OpinionOrderForms frm  
		inner join OpinionOrderFormsResponses resp on resp.form_id = frm.form_id and resp.question_id = eq.id
			where frm.structure_id ='+ convert(varchar(50), @pStructureId) +' and'
			
Declare @SQL3 varchar(max)
set @SQL3 = ''
				
IF @pOrderId <> '' and @pOrderId > 0 and  @pBasketId <> '' and @pBasketId > 0
begin
  set @SQL3 =  '  frm.order_id ='+CONVERT(VARCHAR(50), @pOrderId)
  set @SQL3 = @SQL3 +  ' and frm.basket_id ='+CONVERT(VARCHAR(50), @pBasketId)
end

if @SQL3 ='' 
	begin					
		IF @pOrderId <> '' and @pOrderId > 0
			begin
			  set @SQL3 = '  frm.order_id ='+CONVERT(VARCHAR(50), @pOrderId)
			end
	
		if @SQL3 > '' and @pBasketId > 0
			begin 
				set @SQL3 =@SQL3 + ' and '
			end
 
			
		IF @pBasketId <> '' and @pBasketId > 0
			begin
				set @SQL3 = @SQL3 +  '  frm.basket_id='+CONVERT(VARCHAR(50), @pBasketId)
			end
			
	end	
			 set @SQL2 = @SQL2 + @SQL3 +  ' ) as response_id,
	  eq.id as question_id, eq.code as question_code , eq.question_type_id, tp.code as question_type_code,
	   tp.comment as question_type_comment 
  
	  from OpinionOrderQuestions eq
	  inner join OpinionOrderQuestionsTypes tp on tp.Id = eq.question_type_id
	  ) s
	  left join OpinionOrderFormsResponses resp on resp.reponse_id = s.response_id
	left join OpinionOrderForms frm on frm.form_id = s.form_id'

END
print @SQL2
exec(@SQL2)