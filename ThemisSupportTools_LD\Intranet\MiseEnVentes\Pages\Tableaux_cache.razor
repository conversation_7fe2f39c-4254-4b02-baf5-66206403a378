﻿@page "/"
@layout NoNavLayout
@using Microsoft.EntityFrameworkCore;
@using MiseEnVentes.Models;
@using MiseEnVentes.Shared;
@using MudBlazor
@using System.ComponentModel.DataAnnotations
@using Blazored.Typeahead
@inherits Tableaux_caché
@implements IDisposable

<div style="background-color: black; color: white; min-height: 100vh; padding:2px;">
    <h3 id="titre" class="text-center">Prochaines mises en ventes</h3> <div style="position: fixed; top: 10px; right: 10px; background-color: #222; color: #f4b400; padding: 10px; border-radius: 8px; z-index: 999;">
        ⏳ Dernier rafraîchissement : @lastRefreshTime.ToString("HH:mm:ss")
    </div>

    <a href="admin" class="barre_lien"></a>

    <!-- Tableaux des mises en ventes  -->

    @if (misesEnVente == null)
    {
        <p>Chargement des mises en vente...</p>
    }
    else if (!misesEnVente.Any())
    {
        <p>Aucune mise en vente trouvée.</p>
    }
    else
    {
        <table id="tableaux" class="table table-bordered">
            <tbody>
                @foreach (var group in misesEnVente
                   .Where(m => m.Date.HasValue)
                   .OrderBy(m => m.Date)
                   .GroupBy(m => new { m.Date.Value.Year, m.Date.Value.Month }))
                {

                    @foreach (var vente in group.OrderBy(v => v.Date).ThenBy(v => v.Hour))
                    {
                        var now = DateTime.Now;
                        var dateVente = vente.Date ?? DateOnly.MinValue;
                        var heureVente = vente.Hour ?? TimeOnly.MinValue;
                        var dateTimeVente = dateVente.ToDateTime(heureVente);

                        // Initialise la classe CSS de la ligne à vide
                        string rowClass = "";

                        // Vérifie si la date de la vente correspond à aujourd'hui
                        if (dateVente == DateOnly.FromDateTime(now))
                        {
                            // Si la date et l'heure de la vente sont déjà passées
                            if (dateTimeVente <= now)
                            {
                                rowClass = "vente-passee"; // Marque la ligne comme vente passée
                            } 
                            // Si la vente est prévue dans moins de 3 heures
                            else if (dateTimeVente < now.AddHours(3))
                            {
                                rowClass = "vente-future"; // Marque la ligne comme vente future proche
                            }
                        }

                        <tr class="border">
                            <td class="gris_date  @rowClass" style="border: 4px solid black; ">
                                @(vente.Date?.ToString("ddd dd/MM", new System.Globalization.CultureInfo("fr-FR")))
                            </td>

                            <td class="gris_hour  @rowClass" style="border: 4px solid black; color: #f4b400; font-weight: bold; ">
                                @vente.Hour?.ToString("HH\\:mm")
                            </td>

                            <td class="gris_plateforme @rowClass" style="border: 4px solid black;">
                                @string.Join(", ", vente.MiseEnVentePlateformes.Select(mp => mp.Plateforme.PlateformeName))
                            </td>


                            <td class="gris_structure @rowClass" style="border: 4px solid black;">
                                @{
                                    string nomStructure = "Inconnue";

                                    if (Guid.TryParse(vente.StructureId, out Guid structureGuid)
                                        && AccountBases.TryGetValue(structureGuid, out var account))
                                    {
                                        nomStructure = $"{account.Name} - {account.jav_IDStructure}";
                                    }
                                }
                                @nomStructure
                            </td>

                            <td class="gris_intervenant @rowClass" style="border: 4px solid black;">
                                @string.Join(", ", vente.MiseEnVenteIntervenants.Select(mvi => mvi.Intervenant.IntervenantName.Trim()))
                            </td>

                            <td class="gris_affluence @rowClass" style="border: 4px solid black;">
                            <td>
                                @if (vente.Importance != null)
                                {
                                    var niveau = (ImportanceNiveauCachee)vente.Importance;
                                    <span class="emoji-inline @niveau.GetEmoji()">
                                        @niveau.GetLabel()
                                    </span>
                                }
                                else
                                {
                                    <span class="emoji-inline">❓</span>
                                }
                            </td>
                            </td>

                            <td class="gris_etat @rowClass" style="border: 4px solid black; text-align: center;">
                                @{
                                    string emoji = "❓";
                                    if (Guid.TryParse(vente.StructureId, out Guid sGuid)
                                        && AccountBases.TryGetValue(sGuid, out var acc)
                                        && int.TryParse(acc.jav_IDStructure, out int idStruct)
                                        && fileAttenteTypeByStructure.TryGetValue(idStruct, out var etat))
                                    {
                                        emoji = etat switch
                                        {
                                            "maintenance" => "🛠️",
                                            "v2" => "2️⃣",
                                            "v1" => "1️⃣",
                                            _ => "❓"
                                        };
                                    }
                                }
                                <span class="item_gris_etat">@emoji</span>
                            </td>

                        </tr>
                        @if (@vente.Commentaire != null)
                        {
                        <td colspan="5">
                            <div class="commentaire @rowClass">
                                <label>Commentaire :</label>
                                @vente.Commentaire
                            </div>
                        </td>
                    }
                    }
                }
                </tbody>
            </table>
        }
</div>


