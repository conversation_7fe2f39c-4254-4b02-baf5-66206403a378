﻿/*using MiseEnVentes;
using MiseEnVentes.Interfaces;
using MiseEnVentes.Models;

public class MiseEnVenteService
{
    protected readonly IApplicationDbContext _db;
    public List<MiseEnVenteNew> misesEnVente = new();
    public FormModel formModel = new();
    public Dictionary<Guid, string?> AccountBases { get; set; } = new();
    public bool showPopup;

    public MiseEnVenteService(IApplicationDbContext db)
    {
        _db = db;
    }

    public virtual Task ShowToast() => Task.CompletedTask;

    public async Task HandleValidSubmit()
    {
        var structure = AccountBases.FirstOrDefault(x => x.Value == formModel.Structure);

        var nouvelleVente = new MiseEnVenteNew
        {
            Date = DateOnly.FromDateTime(formModel.Date),
            Hour = TimeOnly.Parse(formModel.Heure!),
            StructureId = structure.Key.ToString(),
            NotifMail = formModel.Mail,
            NotifTicket = formModel.Ticket,
            Commentaire = formModel.Commentaire
        };

        _db.MisesEnVenteNew.Add(nouvelleVente);
        await _db.SaveChangesAsync();

        if (int.TryParse(formModel.Plateforme, out var plateformeId))
        {
            _db.MiseEnVentePlateformes.Add(new MiseEnVentePlateforme
            {
                IdMiseEnVente = nouvelleVente.IdMiseEnVente,
                IdPlateforme = plateformeId
            });
        }

        if (formModel.Intervenants != null)
        {
            foreach (var id in formModel.Intervenants)
            {
                _db.MiseEnVenteIntervenants.Add(new MiseEnVenteIntervenant
                {
                    IdMiseEnVente = nouvelleVente.IdMiseEnVente,
                    IdIntervenant = id
                });
            }
        }

        await _db.SaveChangesAsync();

        misesEnVente.Add(nouvelleVente);
        misesEnVente = misesEnVente.OrderBy(m => m.Date).ToList();
        showPopup = false;
        formModel = new();
        await ShowToast();
    }
}*/
