using Core.Themis.Libraries.DTO.TST;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace ThemisSupportTools.Web.Tests
{
    /// <summary>
    /// Test simple pour vérifier la synchronisation bidirectionnelle entre PRESTATAIREPAIEMENT et Sections complémentaires
    /// </summary>
    public class ConfigIniSynchronizationTest
    {
        /// <summary>
        /// Test de la logique de suppression d'une section des valeurs multiples d'un champ
        /// </summary>
        [Fact]
        public void RemoveSection_ShouldUpdateFieldMultipleValue_WhenSectionIsInFieldMultipleValue()
        {
            // Arrange
            var sectionToRemove = "ATOS";
            var field = new ConfigIniSectionFieldDTO
            {
                FieldName = "PRESTATAIREPAIEMENT",
                FieldMultipleValue = new[] { "ATOS", "ATOS64", "CYBERMUTH" },
                FieldValue = "ATOS,ATOS64,CYBERMUTH"
            };

            var sectionsOfUser = new List<ConfigIniSectionDTO>
            {
                new ConfigIniSectionDTO
                {
                    SectionName = "PRESTATAIREPAIEMENT",
                    SectionFields = new List<ConfigIniSectionFieldDTO> { field }
                }
            };

            // Act - Simuler la logique de RemoveSection
            var allFieldsWithThisSection = sectionsOfUser
                .SelectMany(s => s.SectionFields)
                .Where(f => f.FieldMultipleValue != null && f.FieldMultipleValue.Contains(sectionToRemove))
                .ToList();

            foreach (var fieldToUpdate in allFieldsWithThisSection)
            {
                fieldToUpdate.FieldMultipleValue = fieldToUpdate.FieldMultipleValue
                    .Where(v => v != sectionToRemove).ToArray();
                fieldToUpdate.FieldValue = string.Join(",", fieldToUpdate.FieldMultipleValue);
            }

            // Assert
            Assert.Equal(2, field.FieldMultipleValue.Length);
            Assert.DoesNotContain("ATOS", field.FieldMultipleValue);
            Assert.Contains("ATOS64", field.FieldMultipleValue);
            Assert.Contains("CYBERMUTH", field.FieldMultipleValue);
            Assert.Equal("ATOS64,CYBERMUTH", field.FieldValue);
        }

        /// <summary>
        /// Test que la suppression d'une section inexistante ne cause pas d'erreur
        /// </summary>
        [Fact]
        public void RemoveSection_ShouldNotThrow_WhenSectionNotInFieldMultipleValue()
        {
            // Arrange
            var sectionToRemove = "NONEXISTENT";
            var field = new ConfigIniSectionFieldDTO
            {
                FieldName = "PRESTATAIREPAIEMENT",
                FieldMultipleValue = new[] { "ATOS", "ATOS64", "CYBERMUTH" },
                FieldValue = "ATOS,ATOS64,CYBERMUTH"
            };

            var sectionsOfUser = new List<ConfigIniSectionDTO>
            {
                new ConfigIniSectionDTO
                {
                    SectionName = "PRESTATAIREPAIEMENT",
                    SectionFields = new List<ConfigIniSectionFieldDTO> { field }
                }
            };

            // Act - Simuler la logique de RemoveSection
            var allFieldsWithThisSection = sectionsOfUser
                .SelectMany(s => s.SectionFields)
                .Where(f => f.FieldMultipleValue != null && f.FieldMultipleValue.Contains(sectionToRemove))
                .ToList();

            foreach (var fieldToUpdate in allFieldsWithThisSection)
            {
                fieldToUpdate.FieldMultipleValue = fieldToUpdate.FieldMultipleValue
                    .Where(v => v != sectionToRemove).ToArray();
                fieldToUpdate.FieldValue = string.Join(",", fieldToUpdate.FieldMultipleValue);
            }

            // Assert - Les valeurs ne doivent pas changer
            Assert.Equal(3, field.FieldMultipleValue.Length);
            Assert.Contains("ATOS", field.FieldMultipleValue);
            Assert.Contains("ATOS64", field.FieldMultipleValue);
            Assert.Contains("CYBERMUTH", field.FieldMultipleValue);
            Assert.Equal("ATOS,ATOS64,CYBERMUTH", field.FieldValue);
        }

        /// <summary>
        /// Test de la suppression de la dernière section
        /// </summary>
        [Fact]
        public void RemoveSection_ShouldHandleEmptyResult_WhenRemovingLastSection()
        {
            // Arrange
            var sectionToRemove = "ATOS";
            var field = new ConfigIniSectionFieldDTO
            {
                FieldName = "PRESTATAIREPAIEMENT",
                FieldMultipleValue = new[] { "ATOS" },
                FieldValue = "ATOS"
            };

            var sectionsOfUser = new List<ConfigIniSectionDTO>
            {
                new ConfigIniSectionDTO
                {
                    SectionName = "PRESTATAIREPAIEMENT",
                    SectionFields = new List<ConfigIniSectionFieldDTO> { field }
                }
            };

            // Act - Simuler la logique de RemoveSection
            var allFieldsWithThisSection = sectionsOfUser
                .SelectMany(s => s.SectionFields)
                .Where(f => f.FieldMultipleValue != null && f.FieldMultipleValue.Contains(sectionToRemove))
                .ToList();

            foreach (var fieldToUpdate in allFieldsWithThisSection)
            {
                fieldToUpdate.FieldMultipleValue = fieldToUpdate.FieldMultipleValue
                    .Where(v => v != sectionToRemove).ToArray();
                fieldToUpdate.FieldValue = string.Join(",", fieldToUpdate.FieldMultipleValue);
            }

            // Assert
            Assert.Empty(field.FieldMultipleValue);
            Assert.Equal("", field.FieldValue);
        }
    }
}
