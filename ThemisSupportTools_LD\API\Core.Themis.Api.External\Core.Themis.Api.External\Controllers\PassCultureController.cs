﻿using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange;
using Core.Themis.Libraries.DTO.PassCulture.ApiExchange.Response;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Cryptography;
using System.Text;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Specific Pass culture 
    /// </summary>
    [ApiController]
    [SwaggerTag("Internal")]
    [ApiExplorerSettings(GroupName = "internal")]
    public class PassCultureController : ControllerBase
    {
        private readonly string _hmacKey;
        private readonly IPassCultureService _passCultureService;
        private static readonly RodrigueNLogger Logger = new();

        public PassCultureController(IConfiguration configuration, IPassCultureService passCultureService)
        {
            _hmacKey = configuration["PassCultureHmacKey"]!;
            _passCultureService = passCultureService;
        }

        /// <summary>
        /// Endpoint to connect booking ticketing system to the pass culture application 
        /// </summary>
        /// <param name="structureId">The unique structure identififer</param>
        /// <param name="json">Format</param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BookingSuccess))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPost]
        [Route("api/PassCulture/{structureId}/Booking")]
        public IActionResult Booking(int structureId, [FromBody]string json)
        {
            try
            {
                Logger.Info(structureId, $"PassCulture-Booking ...");

                string? signature = Request.Headers["passculture-signature"];

                Logger.Trace(structureId, $"PassCulture-Signature = {signature}");

                Logger.Debug(structureId, $"PassCulture-json = {json}");

                if (string.IsNullOrWhiteSpace(signature) || !VerifySignature(structureId, json, signature))
                {
                    Logger.Error(structureId, $"Unauthorized PassCulture-Signature");
                    return Unauthorized();
                }

                BookingPayload bookingInfos = JsonConvert.DeserializeObject<BookingPayload>(json)
                    ?? throw new ArgumentNullException($"{nameof(bookingInfos)}");

                WebUser webUser = new()
                {
                    ApplicationPath = $"{Request.Scheme}://{Request.Host.Value}/",
                    AddressIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString(),
                    StartDate = DateTime.Now,
                    SessionWebId = "",
                };

                ResponseBase result = _passCultureService.CreateBookingInRodrigue(structureId, bookingInfos, webUser);

                if (result.GetType() == typeof(BookingError))
                {
                    var error = (BookingError)result;
                    Logger.Error(structureId, error.ErrorMessage);
                    return BadRequest(result);
                }
                else if (result.GetType() == typeof(BookingSuccess))
                {
                    var success = (BookingSuccess)result;
                    var barcodes = success.Tickets.Select(t => t.Barcode);
                    var seats = success.Tickets.Select(t => t.Seat);
                    Logger.Info(structureId, $"PassCulture-Booking barcodes = {string.Join(" | ", barcodes)}");
                    Logger.Info(structureId, $"PassCulture-Booking seats = {string.Join(" | ", seats)}");
                    Logger.Info(structureId, $"PassCulture-Booking remainingQuantity = {success.RemainingQuantity}");
                }

                return Ok(result);
            } 
            catch (Exception ex) 
            { 
                Logger.Error(structureId , $"PassCulture-Booking exception = {ex.Message} \n {ex.StackTrace}");
                return Problem();
            }
        }


        /// <summary>
        /// Endpoint to connect booking cancellation ticketing system to the pass culture application
        /// </summary> 
        /// <param name="structureId"> The unique structure identifier</param>
        /// <param name="payload"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(CancellationSuccess))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPost]
        [Route("api/PassCulture/{structureId}/Cancellation")]
        public IActionResult Cancellation(int structureId, [FromBody]string payload)
        {

            string? signature = Request.Headers["passculture-signature"];
            Logger.Info(structureId, $"PassCulture-Cancellation...");
            
            Logger.Debug(structureId, $"PassCulture-Cancellation-payload = {payload}");
            Logger.Trace(structureId, $"PassCulture-Cancellation-Signature = {signature}");

            if (string.IsNullOrWhiteSpace(signature) || !VerifySignature(structureId, payload, signature))
            {
                Logger.Error(structureId, $"Unauthorized PassCulture-Signature");
                return Unauthorized();
            }

            CancellationPayload cancellingInfos = JsonConvert.DeserializeObject<CancellationPayload>(payload)
                ?? throw new ArgumentNullException($"{nameof(cancellingInfos)}");

            CancellationSuccess result = _passCultureService.CancelBookingInRodrigue(structureId, cancellingInfos);

            return Ok(result);
        }

        /// <summary>
        /// Verify Signature
        /// </summary>
        /// <param name="structureId">The unique structure identififer</param>
        /// <param name="json"></param>
        /// <param name="signature"></param>
        /// <returns></returns>
        private bool VerifySignature(int structureId, string json, string signature)
        {
            using var hmacsha256 = new HMACSHA256(Encoding.UTF8.GetBytes(_hmacKey));
            var hash = hmacsha256.ComputeHash(Encoding.UTF8.GetBytes(json));

            string encodeResult = Convert.ToHexString(hash).ToLower();

            Logger.Trace(structureId, $"Encode result = {encodeResult} && passculture signature = {signature} \n for json = {json}");

            return encodeResult == signature;
        }
    }
}
