﻿using AutoMapper;
//using Core.Themis.API.Authentication.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Structure;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.AccessControl;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Handlers;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;
//using Swashbuckle.Swagger.Annotations;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Security.Claims;
//using WebApi.OutputCache.Core.Time;
//using WebApi.OutputCache.V2;

namespace Core.Themis.API.Authentification.Controllers
{
    public class tokenResponse
    {

        public string authenticationToken { get; set; }


    }


    [ApiController]
    public class AuthenticationController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger _logger;
        private readonly IPartnerManager _partnerManager;
        //private readonly IApiUtilities _apiUtilities;

        private const string RoleOperateurRodrigue = "Rodrigue";

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        public AuthenticationController(IConfiguration configuration, IMemoryCache memoryCache,
            IPartnerManager partnerManager
            //, IApiUtilities apiUtilities
            )
        {
            _partnerManager = partnerManager;
            _configuration = configuration;
            _memoryCache = memoryCache;

            //_apiUtilities = apiUtilities;

        }
        /// <summary>
        /// test the token
        /// </summary>
        /// <remarks>just a test
        /// 
        ///     curl -X 'GET' \
        ///              '/api/token/TestAuth' \
        ///           -H 'accept: */*' \
        ///        -H 'Authorization: aaaaaaa'
        /// 
        /// </remarks>        
        /// <returns></returns>
        [HttpGet]
        [Route("api/token/TestAuth")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(string))]
        [Authorize(Roles = "User,Revendeur,Integrateur,Admin")]
        public IActionResult TestAuth()
        {

            //string theNameFromToken = HttpContext.Current.User.Identity.Name;
            var accT = Request.Headers[HeaderNames.Authorization];
            int id = TokenManager.getPartnerIdFromToken(accT);
            //return Ok($"hello {theNameFromToken}");
            return Ok($"hello {id}");
        }


        /// <summary>
        /// get partner token (1 hour)
        /// </summary>
        /// <remarks>get partner token
        /// 
        /// curl -X 'GET' \
        ///      '/api/994/token/PartnerName' \
        ///      -H 'accept: application/json' \
        ///      -H 'Signature: Iw6xxxxxxxxxxxxxxxxxxVS1s='
        /// 
        /// </remarks>
        /// <param name="username">partner name</param>
        /// <param name="structureId">structure id</param>
        /// <response code="200">Success</response>
        /// <response code="500">Error</response>
        /// <returns>JwtSecurityToken</returns>        
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(tokenResponse))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [HttpGet]
        [Route("api/token/{username}")]        
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        //[CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        public IActionResult GetToken(string username)
        {
            return GetToken(0, username);
        }


            /// <summary>
            /// get partner token (1 hour)
            /// </summary>
            /// <remarks>get partner token
            /// 
            /// curl -X 'GET' \
            ///      '/api/994/token/PartnerName' \
            ///      -H 'accept: application/json' \
            ///      -H 'Signature: Iw6xxxxxxxxxxxxxxxxxxVS1s='
            /// 
            /// </remarks>
            /// <param name="username">partner name</param>
            /// <param name="structureId">structure id</param>
            /// <response code="200">Success</response>
            /// <response code="500">Error</response>
            /// <returns>JwtSecurityToken</returns>        
            [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(tokenResponse))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [HttpGet]
        [Route("api/{structureId}/token/{username}")]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        //[CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        public IActionResult GetToken(int structureId, string username)
        {
            Logger.Debug(structureId, "GetToken ==> " + structureId + " " + username);

            //string typeConn = Initialisations.GetKeyAppSettings("TypeRun");
            var typeConn = _configuration["TypeRun"].ToString();

            try
            {
                PartnerDTO partner = new PartnerDTO();
                //partner = PartnerManager.GetPartnerIdAndRoles(username, typeConn);


                string strNomCache = "structures[" + structureId + "_" + username + "]" + typeConn;
                if (!_memoryCache.TryGetValue(strNomCache, out partner))// Look for cache key.
                {
                    
                     partner = _partnerManager.GetPartnerInfosByNameWithDependancy(username);

                    //_partnerManager.GetRoles()
                  


                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                  //Priority on removing when reaching size limit (memory pressure)
                  .SetPriority(CacheItemPriority.High)
                 // Keep in cache for this time, reset time if accessed.                           
                 // Remove from cache after this time, regardless of sliding expiration
                 .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                    _memoryCache.Set(strNomCache, partner, cacheEntryOptions);
                }

                if (partner != null)
                {
                    //part.LstRolesOfPartner = PartnerManager.GetRolesPartner(part, typeConn);
                    //log.Debug(structureId, "GetToken ==> structures linked to partner, count=" + partner.LstStructuresLinked.Count());
                    Logger.Debug(structureId, "GetToken ==> " + string.Join(",", partner.LstStructuresLinked?.Select(p => p.StructureId)));

                    //bool isRodOp = (partner.LstRolesOfPartner.Where(s => s.PartnerRoleCode == RoleOperateurRodrigue).Count() > 0);
                    bool isRodOp = (partner.LstRolesOfPartner.Where(s => s.PartnerRoleCode == RoleOperateurRodrigue).Count() > 0);

                    //var structuresLinked = partner.LstStructuresLinked.Where(sp => sp.StructureId == structureId || sp.StructureId == 0).FirstOrDefault();

                    if (partner.LstStructuresLinked == null && !isRodOp && structureId != 0)
                    {
                        var pb = Problem(detail: $"structure {structureId} not linked to {username}",
                            statusCode: StatusCodes.Status401Unauthorized);
                        return pb;
                    }
                    if (!isRodOp)
                    {
                        Logger.Debug(structureId, "GetToken ==> structuresLinked count = " + partner.LstStructuresLinked.Count);
                    }
                    else
                    {
                        Logger.Debug(structureId, "GetToken for RodOp");
                    }
                    if (!ApiSignatureManager.CheckSignature(structureId, Request, partner.PartnerId.ToString(), partner.SecretKey, typeConn))
                    {
                        var pb = Problem(detail: $"Signature is missing/incorrect in Header",
                            statusCode: StatusCodes.Status401Unauthorized);
                        return pb;

                    }

                    string audience = _configuration["AudienceToken"];

                    ClaimsIdentity claimIdenttity = PartnerManager.GenerateClaimIdentity(partner);
                    var secretK = _configuration["ApiSecretKey"].ToString();

                    string partnerToken = TokenManager.GenerateTokenPartner(claimIdenttity, audience, secretK);

                    tokenResponse t = new tokenResponse()
                    {
                        authenticationToken = partnerToken
                    };

                    return Ok(t);
                }
                else
                {
                    var pb = Problem(detail: $"GetToken({username},thepassword) return null",
             statusCode: StatusCodes.Status401Unauthorized);
                    return pb;

                }
            }
            catch (Exception ex)
            {
                Logger.Debug(structureId, "GetToken ko " + ex.Message + " " + ex.StackTrace);
                var pb = Problem(detail: $"GetToken ko",
    statusCode: StatusCodes.Status401Unauthorized);
                return pb;



            }
        }

    }
}
