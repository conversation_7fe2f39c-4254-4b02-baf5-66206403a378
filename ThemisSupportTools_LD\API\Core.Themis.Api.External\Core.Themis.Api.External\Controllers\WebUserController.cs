﻿using AutoMapper;

using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// 01. Work on web Users = a user is the representation of a consumer 
    /// </summary>    
    [SwaggerControllerOrder(1)]
 

    [ApiExplorerSettings(GroupName = "ext")]    
    public class WebUserController : ControllerBase
    {

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IWebUserManager _webUserManager;
        private readonly IBuyerProfilManager _buyerProfilManager;

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        private IMapper Mapper
        {
            get;
        }
        private readonly IApiOffersClient _apiOffersClients;

        public WebUserController(IConfiguration configuration, IMemoryCache memoryCache, IMapper mapper,
            IApiOffersClient apiOffersClient,
            IWebUserManager webUserManager,
            IBuyerProfilManager buyerProfilManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            this.Mapper = mapper;
            _apiOffersClients = apiOffersClient;
            _webUserManager = webUserManager;
            _buyerProfilManager = buyerProfilManager;

        }


        /// <summary>
        /// Creation of an internet user / public sells
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <returns>Internet user with an identifier</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Libraries.DTO.exposedObjects.WebUser))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/WebUser")]
        public IActionResult CreateWebUser(int structureId, Libraries.DTO.exposedObjects.WebUser webUser)
        {
            return CreateWebUser(structureId, webUser, 0, "", "");
        }

        /// <summary>
        /// Creation of an internet user for a buyer profil id
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="buyerProfilId">The Buyer profil / Reseller identifier</param>
        /// <returns>Internet user with an identier</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Libraries.DTO.exposedObjects.WebUser))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/WebUser/{buyerProfilId}")]

        public IActionResult CreateWebUser(int structureId, Libraries.DTO.exposedObjects.WebUser webUser, int buyerProfilId)
        {
            return CreateWebUser(structureId, webUser, buyerProfilId, "", "");
        }

        /// <summary>
        /// Creation of an internet user for a buyer profil / reseller
        /// </summary>
        /// <param name="structureId">The structure identifier</param>        
        /// <param name="bpLogin">The Buyer profil / Reseller login</param>
        /// <param name="bpPassword">The Buyer profil / Reseller password</param>
        /// <returns>Internet user with an identifier</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Libraries.DTO.exposedObjects.WebUser))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/WebUser/{bpLogin}/{bpPassword}")]
        public IActionResult CreateWebUser(int structureId, Libraries.DTO.exposedObjects.WebUser webUser, string bpLogin, string bpPassword)
        {
            return CreateWebUser(structureId, webUser, 0, bpLogin, bpPassword);
        }


     
        /// <param name="structureId">The unique structure identifiant</param>
        /// <param name="webUser">The internet user</param>
        /// <param name="buyerProfilId">The Buyer profil / Reseller identifier</param>
        /// <param name="bpLogin">The Buyer profil / Reseller login</param>
        /// <param name="bpPassword">The Buyer profil / Reseller password</param>
        /// <returns></returns>
        private IActionResult CreateWebUser(int structureId, Libraries.DTO.exposedObjects.WebUser webUser, int buyerProfilId, string bpLogin, string bpPassword)
        {

            Logger.Debug(structureId, $"CreateWebUser({structureId}, {buyerProfilId}, {bpLogin}, {bpPassword})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {

                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;

            }
            int myBprofilId = 0;

            if (!string.IsNullOrEmpty(bpLogin) || !string.IsNullOrEmpty(bpPassword))
            {
                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId, bpLogin, bpPassword);
                if (buyerProfil != null)
                {
                    myBprofilId = buyerProfil.Id;
                    if (buyerProfil.IsReseller)
                    {
                        webUser.IdentityId = buyerProfil.IdentityPaiement;
                    }
                }
                else
                {
                    var pb = Problem($"can't retrieve buyer profil", null, StatusCodes.Status401Unauthorized);
                    return pb;
                }
            }            

            Libraries.DTO.WTObjects.WebUser wuDTO =
                (Libraries.DTO.WTObjects.WebUser)Mapper.Map(webUser, typeof(Libraries.DTO.exposedObjects.WebUser), typeof(Libraries.DTO.WTObjects.WebUser));

            wuDTO.ProfilAcheteurId = myBprofilId;

            wuDTO.SessionWebId = "";
            wuDTO.ApplicationPath = $"{Request.Scheme}://{Request.Host.Value}/";
            wuDTO.Browser = "";
            wuDTO.AddressIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString();

            var wu = _webUserManager.SetWT(structureId, wuDTO);
            Libraries.DTO.exposedObjects.WebUser user = (Libraries.DTO.exposedObjects.WebUser)Mapper.Map(wu, typeof(Libraries.DTO.WTObjects.WebUser), typeof(Libraries.DTO.exposedObjects.WebUser));

            return Ok(user);

        }
    }
}