﻿using System.ComponentModel.DataAnnotations;

namespace MiseEnVentes.Models
{
    public class MiseEnVenteNew
    {
        [Key]
        public int IdMiseEnVente { get; set; }

        public DateOnly? Date { get; set; } 
        public TimeOnly? Hour { get; set; } 

        public string? StructureId { get; set; }

        public bool? NotifMail { get; set; }

        public bool? NotifTicket { get; set; }

        public string? Commentaire { get; set; }
        public int? Importance { get; set; }

        public ICollection<MiseEnVenteIntervenant> MiseEnVenteIntervenants { get; set; } = new List<MiseEnVenteIntervenant>();
        public ICollection<MiseEnVentePlateforme> MiseEnVentePlateformes { get; set; } = new List<MiseEnVentePlateforme>();
        
    }
}
