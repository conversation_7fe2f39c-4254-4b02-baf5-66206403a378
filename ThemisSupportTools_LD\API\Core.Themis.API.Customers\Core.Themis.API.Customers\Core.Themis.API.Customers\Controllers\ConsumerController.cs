﻿
using Core.Themis.Libraries.BLL.Managers.Consumer.Interfaces;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using System;

namespace Core.Themis.API.Customers.Controllers
{
    [Authorize(Roles = "Admin, Viewer, User")]
    //[Route("api/[controller]")]
    [ApiController]
    public class ConsumerController : ControllerBase
    {
        private readonly ILogger<ConsumerController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContext;

        private readonly IConsumerManager _consumerManager;

        private static readonly RodrigueNLogger Logger = new();

        public ConsumerController(
            ILogger<ConsumerController> logger, 
            IConfiguration configuration, 
    
            IHttpContextAccessor httpContext,
            IConsumerManager consumerManager)
        {
            _logger = logger;
            _configuration = configuration;
            _httpContext = httpContext;
   
            _consumerManager = consumerManager;
        }

        /// <summary>
        /// insertion d'un consommateur dans la table Open consumer, /!\ pour les profils d'acheteur seulement !!!!!
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="bpId">profil d'acheteur id</param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/consumer/{bpId}")]
        public IActionResult InsertBPConsumer(int structureId, int bpId, [FromBody] string consumName)
        {
            Logger.Trace(structureId, $"InsertBPConsumer({structureId},{bpId},'{consumName}')");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                int ret = _consumerManager.CreateConsumer(structureId, consumName.ToString());
                return Ok(ret);
            }
            catch (Exception ex)
            {
                Logger.Trace(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");

                return Problem(ex.Message, null);
            }
        }
    }
}
