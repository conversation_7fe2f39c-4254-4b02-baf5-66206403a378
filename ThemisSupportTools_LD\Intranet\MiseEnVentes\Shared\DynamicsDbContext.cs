﻿namespace MiseEnVentes.Shared;
using Microsoft.EntityFrameworkCore;
using MiseEnVentes.Models;
using static MiseEnVentes.DonneesDynamicsStructure;

    public class DynamicsDbContext : DbContext
    {
        public DynamicsDbContext(DbContextOptions<DynamicsDbContext> options): base(options){}

    public DbSet<AccountBase> AccountBases { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AccountBase>().ToTable("AccountBase", "dbo");
    }

}


