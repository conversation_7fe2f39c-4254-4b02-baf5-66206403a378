using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Core.Themis.API.External.Helpers
{
    public class BrevoObjects
    {
        public class WebHookItem
        {

            /*{
             "id": xxxxxx,
             "camp_id": xx,
             "email": "<EMAIL>",
             "campaign_name": "My First Campaign",
             "date_sent": "2020-10-09 00:00:00",
             "date_event": "2020-10-09 00:00:00",
             "event": "opened",
             "tag": "",
             "ts_sent": 1604933619,
             "ts_event": 1604933737,
             "ts": 1604937337
           }*/

            public string Event { get; set; }

            public string Email { get; set; }

            public int Id { get; set; }

            [JsonPropertyName("camp_id")]
            public int CampId { get; set; }

            //[JsonPropertyName("campaign_name")]
            //public string CampaignName { get; set; }

            //[JsonPropertyName("date_sent")]
            //public DateTime DateSent { get; set; }

            //[JsonPropertyName("date_event")]
            //public DateTime DateEvent { get; set; }

            public string? Tag { get; set; }

            [JsonPropertyName("ts_sent")]
            public long TsSent { get; set; }

            [JsonPropertyName("ts_event")]
            public long TsEvent { get; set; }
            public long Ts { get; set; }

        }
        public class Campaign
        {
            public Recipients recipients { get; set; }

        }
        public class Recipients
        {
            public List<int> lists { get; set; }
            public List<object> exclusionLists { get; set; }
        }
        public class FolderId
        {
            public string Id { get; set; }

        }
        public class ListResponse
        {
            public int FolderId { get; set; }

        }
    }
}
