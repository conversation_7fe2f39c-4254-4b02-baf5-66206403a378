﻿using System.Collections.Generic;

public class IntervenantManager
{
    public List<int> Intervenants { get; set; } = new List<int>();

    public void OnIntervenantCheckedChanged(int intervenantId, object checkedValue)
    {
        var isChecked = (bool)checkedValue;

        if (isChecked)
        {
            if (!Intervenants.Contains(intervenantId))
                Intervenants.Add(intervenantId);
        }
        else
        {
            if (Intervenants.Contains(intervenantId))
                Intervenants.Remove(intervenantId);
        }
    }
}
