﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.6.33723.286
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ThemisSupportTools", "ThemisSupportTools\ThemisSupportTools.csproj", "{15E4D1CF-3E3E-46F8-B190-B2016D695F89}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{63CC67D7-747F-4ACC-9EA2-5C828B12529B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{5FC86429-82C0-45F0-9A7E-9B5D70E2F30F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{D5B2E25F-C673-4F92-97E7-4241BDE7DB9A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{FCE01C34-BE30-408D-B104-351E3C98CD02}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{FA2793BE-3F97-4090-9987-44538EC9D20E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{EA860B18-94CE-4DA9-AAC1-ADEEF581E260}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{D0448BD3-F9C5-4419-A37E-297195555EF4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Razor", "..\..\Libraries\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor\Core.Themis.Libraries.Razor.csproj", "{06EA81B0-E985-48A4-82D9-AC5155DF2885}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{15E4D1CF-3E3E-46F8-B190-B2016D695F89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{15E4D1CF-3E3E-46F8-B190-B2016D695F89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{15E4D1CF-3E3E-46F8-B190-B2016D695F89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{15E4D1CF-3E3E-46F8-B190-B2016D695F89}.Release|Any CPU.Build.0 = Release|Any CPU
		{63CC67D7-747F-4ACC-9EA2-5C828B12529B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63CC67D7-747F-4ACC-9EA2-5C828B12529B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63CC67D7-747F-4ACC-9EA2-5C828B12529B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63CC67D7-747F-4ACC-9EA2-5C828B12529B}.Release|Any CPU.Build.0 = Release|Any CPU
		{5FC86429-82C0-45F0-9A7E-9B5D70E2F30F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5FC86429-82C0-45F0-9A7E-9B5D70E2F30F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5FC86429-82C0-45F0-9A7E-9B5D70E2F30F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5FC86429-82C0-45F0-9A7E-9B5D70E2F30F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5B2E25F-C673-4F92-97E7-4241BDE7DB9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5B2E25F-C673-4F92-97E7-4241BDE7DB9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5B2E25F-C673-4F92-97E7-4241BDE7DB9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5B2E25F-C673-4F92-97E7-4241BDE7DB9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCE01C34-BE30-408D-B104-351E3C98CD02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCE01C34-BE30-408D-B104-351E3C98CD02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCE01C34-BE30-408D-B104-351E3C98CD02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCE01C34-BE30-408D-B104-351E3C98CD02}.Release|Any CPU.Build.0 = Release|Any CPU
		{FA2793BE-3F97-4090-9987-44538EC9D20E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FA2793BE-3F97-4090-9987-44538EC9D20E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FA2793BE-3F97-4090-9987-44538EC9D20E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FA2793BE-3F97-4090-9987-44538EC9D20E}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA860B18-94CE-4DA9-AAC1-ADEEF581E260}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA860B18-94CE-4DA9-AAC1-ADEEF581E260}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA860B18-94CE-4DA9-AAC1-ADEEF581E260}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA860B18-94CE-4DA9-AAC1-ADEEF581E260}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0448BD3-F9C5-4419-A37E-297195555EF4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0448BD3-F9C5-4419-A37E-297195555EF4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0448BD3-F9C5-4419-A37E-297195555EF4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0448BD3-F9C5-4419-A37E-297195555EF4}.Release|Any CPU.Build.0 = Release|Any CPU
		{06EA81B0-E985-48A4-82D9-AC5155DF2885}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06EA81B0-E985-48A4-82D9-AC5155DF2885}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06EA81B0-E985-48A4-82D9-AC5155DF2885}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06EA81B0-E985-48A4-82D9-AC5155DF2885}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CD82DB30-1CBA-42D5-84E4-DB57E14D03DC}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 10
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName1 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath1 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName2 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName8 = ThemisSupportTools\\ThemisSupportTools.csproj
		SccProjectName8 = ThemisSupportTools
		SccLocalPath8 = ThemisSupportTools
		SccProjectUniqueName9 = ..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj
		SccProjectName9 = ../../Libraries/Core.Themis.Libraries.Razor/Core.Themis.Libraries.Razor
		SccLocalPath9 = ..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor
	EndGlobalSection
EndGlobal
