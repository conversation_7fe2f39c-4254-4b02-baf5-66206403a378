﻿/*  SelectCiblesFromListIdentite.sql */
SELECT identite_id, identite_nom, identite_prenom, postal_tel[EMAILCOL] as email 
,postal_pays as country
,postal_ville as city
,postal_cp as cp
,postal_rue1 as adress1
,postal_rue2 as adress2
,postal_tel1 as tel1
,postal_tel2 as tel2
,postal_tel3 as tel3
,postal_tel4 as tel4
,postal_tel5 as tel5
,postal_tel6 as tel6
,postal_tel7 as tel7
,appellation_nom as appellation
,identite_complement as interlocuteur,

  STUFF((
            --SELECT ',' + convert(varchar,ic.info_comp_id)
			SELECT ',' + valeur_param1
			
            FROM identite_infos_comp iic 
			inner join info_comp ic on ic.info_comp_id = iic.info_comp_id 
			where iic.identite_id = i.identite_id and ic.valeur_param1 like 'ATML%' and iic.supprimer ='N'  order by valeur_param1
            FOR XML PATH('')
            ), 1, 1, '') as listATML /* liste mailing list ActiveTrail */

FROM identite i left outer join  global_appellation gp ON   i.appellation_id = gp.appellation_id
WHERE identite_id in ([LISTIDENTITEID])
 --AND postal_tel[EMAILCOL] is not null and postal_tel[EMAILCOL] <> '' 
 ORDER BY identite_id