﻿using System.ComponentModel.DataAnnotations;

namespace MiseEnVentes.Models
{
    public class Intervenant
    {
        [Key]
        public int IdIntervenant { get; set; }
        public string IntervenantName { get; set; } = string.Empty;
        public string IntervenantEmail { get; set; } = string.Empty;

        public ICollection<MiseEnVenteIntervenant> MiseEnVenteIntervenants { get; set; } = new List<MiseEnVenteIntervenant>();


    }
}
