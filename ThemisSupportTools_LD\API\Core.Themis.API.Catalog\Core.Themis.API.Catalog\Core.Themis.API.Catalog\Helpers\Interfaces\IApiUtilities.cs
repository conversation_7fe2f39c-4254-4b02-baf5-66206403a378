﻿using Core.Themis.Libraries.DTO;
using System.Data.SqlClient;

namespace Core.Themis.API.Catalog.Helpers.Interfaces
{
    public interface IApiUtilities
    {

        //SqlConnection GetConnexionOpen(int structureId, string typeRun);

        //PartnerDTO GetPartnerIdAndRoles(string partnerName);

        //PartnerDTO GetPartnerIdAndRoles(int partnerId);

        //SqlConnection GetConnexionWebLibrary();
    }
}
